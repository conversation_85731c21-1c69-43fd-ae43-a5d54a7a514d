<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Cart extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'session_id',
        'type',
        'provider_id',
        'subtotal',
        'discount_amount',
        'coupon_code',
        'booking_fee',
        'home_service_fee',
        'delivery_fee',
        'total',
        'loyalty_points_used'
    ];

    protected $casts = [
        'subtotal' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'booking_fee' => 'decimal:2',
        'home_service_fee' => 'decimal:2',
        'delivery_fee' => 'decimal:2',
        'total' => 'decimal:2',
        'loyalty_points_used' => 'integer'
    ];

    /**
     * Get the user that owns the cart
     */
    public function user()
    {
        return $this->belongsTo(User::class)->withTrashed();
    }

    /**
     * Get the provider associated with the cart
     */
    public function provider()
    {
        return $this->belongsTo(Provider::class)->withTrashed();
    }

    /**
     * Get the cart items
     */
    public function items()
    {
        return $this->hasMany(CartItem::class);
    }

    /**
     * Get the coupon applied to the cart
     */
    public function coupon()
    {
        return $this->belongsTo(Coupon::class, 'coupon_code', 'coupon_num');
    }

    /**
     * Check if cart has services
     */
    public function hasServices()
    {
        return $this->items()->where('item_type', 'App\Models\Service')->exists();
    }

    /**
     * Check if cart has products
     */
    public function hasProducts()
    {
        return $this->items()->where('item_type', 'App\Models\Product')->exists();
    }

    /**
     * Get total items count
     */
    public function getTotalItemsAttribute()
    {
        return $this->items()->sum('quantity');
    }

    /**
     * Get services total
     */
    public function getServicesSubtotalAttribute()
    {
        return $this->items()
            ->where('item_type', 'App\Models\Service')
            ->sum('total');
    }

    /**
     * Get products total
     */
    public function getProductsSubtotalAttribute()
    {
        return $this->items()
            ->where('item_type', 'App\Models\Product')
            ->sum('total');
    }
}
