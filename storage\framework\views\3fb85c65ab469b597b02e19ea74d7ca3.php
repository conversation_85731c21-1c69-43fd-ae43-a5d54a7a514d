

<div class="position-relative">
    
    
    

    
    
 <table class="table table-bordered">
    <thead>
        <tr>
            <th>#</th>
            <th><?php echo e(__('admin.name')); ?></th>
            <th><?php echo e(__('admin.email')); ?></th>
            <th><?php echo e(__('admin.phone')); ?></th>
            <th><?php echo e(__('admin.last_message')); ?></th>
            <th><?php echo e(__('admin.last_message_date')); ?></th>
            <th><?php echo e(__('admin.control')); ?></th>
        </tr>
    </thead>
    <tbody>
        <?php $__currentLoopData = $clients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $client): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php
                $lastMessage = $client->consultationMessages()->latest()->first();
            ?>
            <tr>
                <td><?php echo e($client->id); ?></td>
                <td><?php echo e($client->name); ?></td>
                <td><?php echo e($client->email); ?></td>
                <td><?php echo e($client->phone); ?></td>
                <td>
                    <?php if($lastMessage): ?>
                        <?php echo e(Str::limit($lastMessage->message, 50)); ?>

                    <?php else: ?>
                        <span class="text-muted"><?php echo e(__('admin.no_messages')); ?></span>
                    <?php endif; ?>
                </td>
                <td>
                    <?php if($lastMessage): ?>
                        <?php echo e($lastMessage->created_at->format('Y-m-d H:i')); ?>

                    <?php else: ?>
                        <span class="text-muted">-</span>
                    <?php endif; ?>
                </td>
                <td>
                    <a href="<?php echo e(route('admin.consultations.show', $client->id)); ?>" class="btn btn-warning btn-sm p-1"> <i class="feather icon-eye"></i>
                    </a>
                </td>
            </tr>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </tbody>
</table>


    
    <?php if($clients->count() == 0): ?>
        <div class="d-flex flex-column w-100 align-center mt-4">
            <img src="<?php echo e(asset('admin/app-assets/images/pages/404.png')); ?>" alt="">
            <span class="mt-2" style="font-family: cairo"><?php echo e(__('admin.there_are_no_matches_matching')); ?></span>
        </div>
    <?php endif; ?>
    

</div>


        <?php if($clients->count() > 0 && $clients instanceof \Illuminate\Pagination\AbstractPaginator ): ?>
    <div class="d-flex justify-content-center mt-3">
        <?php echo e($clients->links()); ?>

    </div>
<?php endif; ?>

<?php /**PATH D:\Workstation\Taswk\sorriso-backend\resources\views/admin/consultation_messages/table.blade.php ENDPATH**/ ?>