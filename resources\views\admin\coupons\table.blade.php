<div class="position-relative">
    {{-- table loader  --}}
    {{-- <div class="table_loader" >
        {{__('admin.loading')}}
    </div> --}}
    {{-- table loader  --}}
    {{-- table content --}}
    <table class="table " id="tab">
        <thead>
            <tr>
                <th>
                    <label class="container-checkbox">
                        <input type="checkbox" value="value1" name="name1" id="checkedAll">
                        <span class="checkmark"></span>
                    </label>
                </th>
                <th>{{ __('admin.id_num') }}</th>
                <th>{{ __('admin.date') }}</th>
                <th>{{ __('admin.coupon_name') }}</th>
                <th>{{ __('admin.coupon_number') }}</th>
                <th>{{ __('admin.provider') }}</th>
                <th>{{ __('admin.discount_type') }}</th>
                <th>{{ __('admin.discount_value') }}</th>
                <th>{{ __('admin.start_date') }}</th>
                <th>{{ __('admin.expiry_date') }}</th>
                <th>{{ __('admin.used_times') }}</th>
                <th>{{ __('admin.expired') }}</th>

                <th>{{ __('admin.status') }}</th>
                <th>{{ __('admin.control') }}</th>
            </tr>
        </thead>
        <tbody>
            @foreach($coupons as $coupon)
        
                <tr class="delete_coupon">
                    <td class="text-center">
                        <label class="container-checkbox">
                            <input type="checkbox" class="checkSingle" id="{{$coupon->id}}">
                            <span class="checkmark"></span>
                        </label>
                    </td>
                    <td>{{ $coupon->id }}</td>
                    <td>{{\Carbon\Carbon::parse($coupon->created_at)->format('d/m/Y')}}</td>
                    <td>{{$coupon->coupon_name ?? __('admin.not_set')}}</td>
                    <td>{{$coupon->coupon_num}}</td>
                    <td>
                        <span class="badge badge-info">{{$coupon->provider->commercial_name ?? __('admin.provider_not_found')}}</span>
                    </td>
                    <td>
                        <span class="badge badge-{{$coupon->type == 'ratio' ? 'warning' : 'primary'}}">
                            {{$coupon->type == 'ratio' ? __('admin.percentage') : __('admin.fixed_number')}}
                        </span>
                    </td>
                    <td>
                        {{$coupon->discount}}{{$coupon->type == 'ratio' ? '%' : ''}}
                        @if($coupon->max_discount && $coupon->type == 'ratio')
                            <small class="text-muted">({{__('admin.max')}}: {{$coupon->max_discount}})</small>
                        @endif
                    </td>
                   
                    <td>{{$coupon->start_date ? date('d-m-Y', strtotime($coupon->start_date)) : __('admin.not_set')}}</td>
                    <td>{{$coupon->expire_date ? date('d-m-Y', strtotime($coupon->expire_date)) : __('admin.no_expiry')}}</td>
                    <td>
                        <a href="{{ route('admin.coupons.orders', ['coupon' => $coupon->id]) }}">
                            {{ $coupon->usage_time }}
                        </a>
                    </td>
                  
                    <td>
                        @if ($coupon->is_valid)

                            <span class="badge badge-success">{{ __('admin.valid') }}</span>
                        @else
                            <span class="badge badge-danger">{{ __('admin.expired') }}</span>
                        @endif
                    </td>
                    
                    
                    <td>
                        {!! toggleBooleanView($coupon , route('admin.model.active' , ['model' =>'Coupon' , 'id' => $coupon->id , 'action' => 'is_active'])) !!}
                    </td>

                    <td class="product-action">
                        <span class="text-primary"><a href="{{ route('admin.coupons.show', ['id' => $coupon->id]) }}" class="btn btn-warning btn-sm p-1" title="{{ __('admin.show') }}"><i class="feather icon-eye"></i></a></span>
                        <span class="action-edit text-primary"><a href="{{ route('admin.coupons.edit', ['id' => $coupon->id]) }}" class="btn btn-primary btn-sm p-1" title="{{ __('admin.edit') }}"><i class="feather icon-edit"></i></a></span>
                        <span class="delete-row btn btn-danger btn-sm p-1" data-url="{{ url('admin/coupons/' . $coupon->id) }}" title="{{ __('admin.delete') }}"><i class="feather icon-trash"></i></span>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
    {{-- table content --}}
    {{-- no data found div --}}
    @if ($coupons->count() == 0)
        <div class="d-flex flex-column w-100 align-center mt-4">
            <img src="{{asset('admin/app-assets/images/pages/404.png')}}" alt="">
            <span class="mt-2" style="font-family: cairo">{{__('admin.there_are_no_matches_matching')}}</span>
        </div>
    @endif
    {{-- no data found div --}}

</div>
{{-- pagination  links div --}}
@if ($coupons->count() > 0 && $coupons instanceof \Illuminate\Pagination\AbstractPaginator )
    <div class="d-flex justify-content-center mt-3">
        {{$coupons->links()}}
    </div>
@endif
{{-- pagination  links div --}}