<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\Myfatoorah\OrderPaymentService;
use App\Services\Myfatoorah\CoursePaymentService;
use App\Models\Order;
use App\Models\CourseEnrollment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class UnifiedPaymentController extends Controller
{
    protected $orderPaymentService;
    protected $coursePaymentService;

    public function __construct(OrderPaymentService $orderPaymentService, CoursePaymentService $coursePaymentService)
    {
        $this->orderPaymentService = $orderPaymentService;
        $this->coursePaymentService = $coursePaymentService;
    }

    /**
     * Handle webhook from MyFatoorah
     */
    public function webhook(Request $request)
    {
        try {
            // Validate webhook signature if configured
           
            $data = $request->all();
            Log::info('MyFatoorah unified payment webhook received', $data);

            // Extract payment ID from simplified webhook payload
            $paymentId = $data['paymentId'] ?? $data['Id'] ?? null;

            if (!$paymentId) {
                Log::warning('MyFatoorah webhook missing payment ID', $data);
                return response('Missing payment ID', 400);
            }

            // Fetch full payment details from MyFatoorah API
            $paymentDetails = $this->fetchPaymentDetails($paymentId);
            
            if (!$paymentDetails) {
                Log::error('Failed to fetch payment details from MyFatoorah', [
                    'payment_id' => $paymentId
                ]);
                return response('Failed to fetch payment details', 500);
            }

            // Extract information from full payment details
            $status = $paymentDetails['InvoiceStatus'] ?? null;
            $entityId = $paymentDetails['CustomerReference'] ?? null;
            $paymentType = $paymentDetails['UserDefinedField'] ?? null;

            Log::info('Payment details fetched from MyFatoorah', [
                'payment_id' => $paymentId,
                'status' => $status,
                'entity_id' => $entityId,
                'payment_type' => $paymentType
            ]);

            // Process the webhook based on payment status
            switch ($status) {
                case 'Paid':
                case 'DuplicatePayment':
                    return $this->handlePaymentStatusChanged($paymentId, $entityId, $paymentType, $paymentDetails);
                
                case 'Failed':
                case 'Cancelled':
                case 'Expired':
                    return $this->handlePaymentFailed($paymentId, $entityId, $paymentType, $paymentDetails);
                
                default:
                    Log::info('Unhandled MyFatoorah payment status', [
                        'payment_id' => $paymentId,
                        'status' => $status,
                        'entity_id' => $entityId,
                        'payment_type' => $paymentType
                    ]);
                    return response('OK', 200);
            }

        } catch (\Exception $e) {
            Log::error('MyFatoorah unified payment webhook processing failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response('Webhook processing failed', 500);
        }
    }

    /**
     * Fetch payment details from MyFatoorah API
     */
    private function fetchPaymentDetails($paymentId)
    {
        try {
            $config = config('myfatoorah');
            $myfatoorahApi = new \App\Services\Myfatoorah\PaymentMyfatoorahApiV2(
                $config['api_key'],
                $config['test_mode'],
                $config['log_enabled'] ? $config['log_file'] : null
            );

            $responseData = $myfatoorahApi->getPaymentStatus($paymentId, 'PaymentId');
            $responseArray = json_decode(json_encode($responseData), true);

            Log::info('Payment details fetched successfully', [
                'payment_id' => $paymentId,
                'response' => $responseArray
            ]);

            return $responseArray;

        } catch (\Exception $e) {
            Log::error('Failed to fetch payment details from MyFatoorah', [
                'payment_id' => $paymentId,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * Handle successful payment status change
     */
    private function handlePaymentStatusChanged($paymentId, $entityId, $paymentType, $paymentDetails)
    {
        return DB::transaction(function () use ($paymentId, $entityId, $paymentType, $paymentDetails) {
            try {
                // Determine payment type from UserDefinedField
                $type = $this->determinePaymentType($paymentType);
                if (!$type) {
                    Log::warning('Unknown payment type in webhook', [
                        'payment_type' => $paymentType,
                        'payment_id' => $paymentId
                    ]);
                    return response('Unknown payment type', 400);
                }

                Log::info("Processing successful {$type} payment status change", [
                    'payment_id' => $paymentId,
                    'entity_id' => $entityId
                ]);

                // Call the appropriate verification method based on type
                $result = $this->verifyPayment($paymentId, $entityId, $type);
                if ($result['success']) {
                    $entityName = $type === 'order' ? 'order' : 'enrollment';
                    
                    

                    return view('payment.success', ['message' => 'Payment ID is required']);
                } else {
                    Log::warning("{$type} payment webhook verification failed", [
                        'payment_id' => $paymentId,
                        'entity_id' => $entityId,
                        'type' => $type,
                        'error' => $result['message']
                    ]);

                    return view('payment.fail');
                }

            } catch (\Exception $e) {
                Log::error("Failed to process {$type} payment status change", [
                    'payment_id' => $paymentId,
                    'entity_id' => $entityId,
                    'type' => $type,
                    'error' => $e->getMessage()
                ]);

                return response('Status change handling failed', 500);
            }
        });
    }

    /**
     * Handle failed payment
     */
    private function handlePaymentFailed($paymentId, $entityId, $paymentType, $paymentDetails)
    {
        return DB::transaction(function () use ($paymentId, $entityId, $paymentType, $paymentDetails) {
            try {
                // Determine payment type from UserDefinedField
                $type = $this->determinePaymentType($paymentType);
                if (!$type) {
                    Log::warning('Unknown payment type in webhook', [
                        'payment_type' => $paymentType,
                        'payment_id' => $paymentId
                    ]);
                    return response('Unknown payment type', 400);
                }

                Log::info("Processing failed {$type} payment", [
                    'payment_id' => $paymentId,
                    'entity_id' => $entityId
                ]);

                // Find entity by ID
                $entity = $this->findEntity($entityId, $type);
                if (!$entity) {
                    Log::warning("{$type} entity not found for failed payment webhook", [
                        'payment_id' => $paymentId,
                        'entity_id' => $entityId,
                        'type' => $type
                    ]);
                    return response('Entity not found', 404);
                }

                // Update entity status to failed
                $this->updateEntityToFailed($entity, $type);

                Log::info("{$type} payment failed webhook processed", [
                    'payment_id' => $paymentId,
                    'entity_id' => $entity->id,
                    'type' => $type,
                    'status' => $entity->status ?? $entity->current_status,
                    'payment_status' => $entity->payment_status
                ]);

                return view('payment.fail');

            } catch (\Exception $e) {
                Log::error("{$type} payment failed webhook handling failed", [
                    'payment_id' => $paymentId,
                    'entity_id' => $entityId,
                    'type' => $type,
                    'error' => $e->getMessage()
                ]);

                return response('Failed payment handling failed', 500);
            }
        });
    }

    /**
     * Verify payment based on type
     */
    private function verifyPayment($paymentId, $entityId, $type)
    {
        if ($type === 'order') {
            return $this->orderPaymentService->verifyOrderPayment($paymentId, $entityId);
        } else {
            return $this->coursePaymentService->verifyCoursePayment($paymentId, $entityId);
        }
    }

    /**
     * Find entity by ID and type
     */
    private function findEntity($entityId, $type)
    {
        if ($type === 'order') {
            return Order::withTrashed()->find($entityId);
        } else {
            return CourseEnrollment::find($entityId);
        }
    }

    /**
     * Update entity status to failed
     */
    private function updateEntityToFailed($entity, $type)
    {
        if ($type === 'order') {
            $entity->update([
                'payment_status' => 'failed',
                'current_status' => 'cancelled',
            ]);
        } else {
            $entity->update([
                'payment_status' => 'failed',
                'status' => 'cancelled',
            ]);
        }
    }

    /**
     * Validate webhook signature
     */
    private function validateWebhookSignature($data, $secret, $signature)
    {
        try {
            uksort($data, 'strcasecmp');
            
            $output = implode(',', array_map(
                function ($v, $k) {
                    return sprintf("%s=%s", $k, $v);
                },
                $data,
                array_keys($data)
            ));

            $hash = base64_encode(hash_hmac('sha256', $output, $secret, true));
            
            return $signature === $hash;

        } catch (\Exception $e) {
            Log::error('Webhook signature validation failed', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Determine payment type from UserDefinedField
     */
    private function determinePaymentType($userDefinedField)
    {
        return match ($userDefinedField) {
            'order_payment' => 'order',
            'course_enrollment' => 'course',
            default => null
        };
    }

    /**
     * Show payment success page for browser callback
     */
    public function paymentSuccess(Request $request)
    {
        $paymentId = $request->get('paymentId');
        if (!$paymentId) {
            return view('payment.fail');
        }
        // Try both order and course
        $result = $this->orderPaymentService->verifyOrderPayment($paymentId);
        if (!$result['success']) {
            $result = $this->coursePaymentService->verifyCoursePayment($paymentId);
        }
        if ($result['success']) {
            return view('payment.success', [
                'message' => 'Payment completed successfully!',
                'entity' => $result['order'] ?? $result['enrollment'] ?? null,
                'order_number' => $result['order']->order_number ?? $result['enrollment']->id ?? null,
            ]);
        } else {
            return view('payment.fail');
        }
    }

    /**
     * Show payment fail page for browser callback
     */
    public function paymentFail(Request $request)
    {
        $paymentId = $request->get('paymentId');
        if (!$paymentId) {
            return view('payment.fail');
        }
        // Try both order and course
        $result = $this->orderPaymentService->verifyOrderPayment($paymentId);
        if (!$result['success']) {
            $result = $this->coursePaymentService->verifyCoursePayment($paymentId);
        }
        return view('payment.fail');

    }
} 