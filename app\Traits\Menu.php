<?php

namespace App\Traits;

trait menu {
  public function home() {

    $menu = [
     
      [
        'name'  => __('admin.sections'),
        'count' => \App\Models\Category::count(),
        'icon'  => 'icon-users',
        'url'   => url('admin/categories'),
      ],
      [
        'name'  => __('admin.services'),
        'count' => \App\Models\Service::count(),
        'icon'  => 'icon-users',
        'url'   => url('admin/services'),
      ],

      [
        'name'  => __('admin.product-categories'),
        'count' => \App\Models\ProductCategory::count(),
        'icon'  => 'icon-users',
        'url'   => url('admin/products'),
      ],
      
      [
        'name'  => __('admin.products'),
        'count' => \App\Models\Product::count(),
        'icon'  => 'icon-users',
        'url'   => url('admin/products'),
      ],

      [
        'name'  => __('admin.orders_only_services'),
        'count' => \App\Models\Order::whereHas('items', function($q) {
            $q->where('item_type', 'App\\Models\\Service');
        })->whereDoesntHave('items', function($q) {
            $q->where('item_type', 'App\\Models\\Product');
        })->where('current_status', '!=', 'cancelled')->sum('total'),
        'icon'  => 'icon-list',
      ],
      [
        'name'  => __('admin.orders_only_products'),
        'count' => \App\Models\Order::whereHas('items', function($q) {
            $q->where('item_type', 'App\\Models\\Product');
        })->where('current_status', '!=', 'cancelled')->sum('total'),
        'icon'  => 'icon-list',
      ],
      [
        'name'  => __('admin.total_orders'),
        'count' => \App\Models\Order::sum('total'),
        'icon'  => 'icon-list',
      ],

      [
        'name'  => __('admin.platform_commission'),
        'count' => \App\Models\Order::where('current_status', '!=', 'cancelled')->sum('platform_commission'),
        'icon'  => 'icon-list',
      ],

      [
        'name'  => __('admin.delivery_fees'),
        'count' => \App\Models\Order::sum('delivery_fee'),
        'icon'  => 'icon-list',
      ],

      [
        'name'  => __('admin.cancel_fees'),
        'count' => \App\Models\Order::sum('cancel_fees'),
        'icon'  => 'icon-list',
      ],

      [
        'name'  => __('admin.booking_fees'),
        'count' => \App\Models\Order::sum('booking_fee'),
        'icon'  => 'icon-list',
      ],

      [
        'name'  => __('admin.total_discounts'),
        'count' => \App\Models\Order::whereNotNull('coupon_id')->sum('discount_amount'),
        'icon'  => 'icon-list',
      ],

      [
        'name'  => __('admin.total_revenue'),
        'count' =>
            \App\Models\Order::where('current_status', '!=', 'cancelled')->sum('platform_commission')
            + \App\Models\Order::sum('cancel_fees')
            + \App\Models\Order::sum('booking_fee'),
        'icon'  => 'icon-list',
      ],

      [
        'name'  => __('admin.currnet_provider_share'),
        'count' =>\App\Models\Provider::sum('wallet_balance'),
        'icon'  => 'icon-list',
      ],

      [
        'name'  => __('admin.total_provider_share'),
        'count' => \App\Models\Provider::sum('wallet_balance') + \App\Models\Provider::sum('withdrawable_balance'),
        'icon'  => 'icon-list',
      ],

      // 6. Total clients
      [
        'name'  => __('admin.total_clients'),
        'count' => \App\Models\User::where('type', 'client')->count(),
        'icon'  => 'icon-users',
      ],
      // 17. Total client account deletion requests
      [
        'name'  => __('admin.total_client_deletion_requests'),
        'count' => \App\Models\AccountDeletionRequest::whereHas('user', function($q) {
            $q->where('type', 'client');
        })->count(),
        'icon'  => 'icon-users',
      ],
      // 18. Total blocked clients
      [
        'name'  => __('admin.total_blocked_clients'),
        'count' => \App\Models\User::where('type', 'client')->where('status', 'blocked')->count(),
        'icon'  => 'icon-users',
      ],
      [
        'name'  => __('admin.total_deleted_clients'),
        'count' => \App\Models\User::withTrashed()->where('type', 'client')->whereNotNull('deleted_at')->count(),
        'icon'  => 'icon-users',
    ],
    
      // 19. Total service orders
      [
        'name'  => __('admin.total_service_orders'),
        'count' => \App\Models\OrderItem::where('item_type', 'App\\Models\\Service')->count(),
        'icon'  => 'icon-list',
      ],
      // 20. Total provider registration requests
      [
        'name'  => __('admin.total_provider_registration_requests'),
        'count' => \App\Models\Provider::where('status', 'in_review')->count(),
        'icon'  => 'icon-users',
      ],
      // 21. Total provider account deletion requests
      [
        'name'  => __('admin.total_provider_deletion_requests'),
        'count' => \App\Models\AccountDeletionRequest::whereHas('user', function($q) {
            $q->where('type', 'provider');
        })->count(),
        'icon'  => 'icon-users',
      ],

      [
        'name'  => __('admin.total_deleted_providers'),
        'count' => \App\Models\Provider::withTrashed()->whereNotNull('deleted_at')->count(),
        'icon'  => 'icon-users',
      ],
      // 22. Total blocked providers
      [
        'name'  => __('admin.total_blocked_providers'),
        'count' => \App\Models\Provider::where('status', 'blocked')->count(),
        'icon'  => 'icon-users',
      ],
      // 23. Total active providers
      [
        'name'  => __('admin.total_active_providers'),
        'count' => \App\Models\Provider::where('status', 'accepted')->whereNull('deleted_at')->count(),
        'icon'  => 'icon-users',
      ],
     
     
      [
        'name'  => __('admin.providers'),
        'count' => \App\Models\Provider::whereNull('deleted_at')->count(),
        'icon'  => 'icon-users',
      ],
      // 24. Total bookings
      [
        'name'  => __('admin.total_bookings'),
        'count' => \App\Models\Order::count(),
        'icon'  => 'icon-list',
      ],
      // 25. Total bookings pending payment confirmation
      [
        'name'  => __('admin.total_bookings_pending_payment_confirmation'),
        'count' => \App\Models\Order::where('current_status', 'pending_payment')->count(),
        'icon'  => 'icon-list',
      ],
      // 26. Total bookings under processing
      [
        'name'  => __('admin.total_bookings_processing'),
        'count' => \App\Models\Order::where('current_status', 'processing')->count(),
        'icon'  => 'icon-list',
      ],
      // 27. Total ongoing bookings
      [
        'name'  => __('admin.total_bookings_ongoing'),
        'count' => \App\Models\Order::where('current_status', 'ongoing')->count(),
        'icon'  => 'icon-list',
      ],
      // 28. Total cancelled bookings
      [
        'name'  => __('admin.total_bookings_cancelled'),
        'count' => \App\Models\Order::where('current_status', 'cancelled')->count(),
        'icon'  => 'icon-list',
      ],
      // 29. Total completed bookings
      [
        'name'  => __('admin.total_bookings_completed'),
        'count' => \App\Models\Order::where('current_status', 'completed')->count(),
        'icon'  => 'icon-list',
      ],
      // 30. Total failed bookings
      [
        'name'  => __('admin.total_bookings_failed'),
        'count' => \App\Models\Order::where('current_status', 'failed')->count(),
        'icon'  => 'icon-list',
      ],  
      [
        'name'  => __('admin.total_consultation_messages'),
        'count' => \App\Models\ConsultationMessage::distinct('client_id')->count('user_id'),
        'icon'  => 'icon-list',
      ],
      // 32. Total unread contact us messages
      [
        'name'  => __('admin.total_unread_contactus_messages'),
        'count' => \App\Models\ContactUs::where('is_read', 0)->count(),
        'icon'  => 'icon-list',
      ],
    ];

    return $menu;
  }

  

}