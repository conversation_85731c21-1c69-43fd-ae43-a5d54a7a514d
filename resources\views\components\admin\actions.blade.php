<div class="product-action">
    {{-- Visible on larger screens --}}
    <span class="d-none d-md-inline">
        <a href="{{ $showUrl }}" class="btn btn-warning btn-sm p-1" title="{{ __('admin.show') }}"><i class="feather icon-eye"></i></a>
    </span>
    <span class="d-none d-md-inline">
        <a href="{{ $editUrl }}" class="btn btn-primary btn-sm p-1" title="{{ __('admin.edit') }}"><i class="feather icon-edit"></i></a>
    </span>
    <span class="d-none d-md-inline">
        <button class="delete-row btn btn-danger btn-sm p-1" data-url="{{ $deleteUrl }}" title="{{ __('admin.delete') }}"><i class="feather icon-trash"></i></button>
    </span>

    {{-- Dropdown for smaller screens --}}
    <span class="actions-dropdown d-md-none">
        <div class="dropdown">
            <button class="btn btn-sm btn-secondary dropdown-toggle" type="button" id="actions-menu-{{ $id }}" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                {{ __('admin.actions') }}
            </button>
            <div class="dropdown-menu" aria-labelledby="actions-menu-{{ $id }}">
                <a class="dropdown-item" href="{{ $showUrl }}">{{ __('admin.show') }}</a>
                <a class="dropdown-item" href="{{ $editUrl }}">{{ __('admin.edit') }}</a>
                <button class="dropdown-item delete-row" data-url="{{ $deleteUrl }}">{{ __('admin.delete') }}</button>
            </div>
        </div>
    </span>
</div>