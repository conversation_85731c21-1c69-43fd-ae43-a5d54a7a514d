<div class="position-relative">
    
    
    

    
    <table class="table " id="tab">
        <thead>
            <tr>
                <th>
                    <label class="container-checkbox">
                        <input type="checkbox" value="value1" name="name1" id="checkedAll">
                        <span class="checkmark"></span>
                    </label>
                </th>
                <th><?php echo e(__('admin.id_num')); ?></th>
                <th><?php echo e(__('admin.commercial_name')); ?></th>
                <th><?php echo e(__('admin.email')); ?></th>
                <th><?php echo e(__('admin.phone')); ?></th>
                <th><?php echo e(__('admin.phone_status')); ?></th>
                <th><?php echo e(__('admin.provider_status')); ?></th>
                <th><?php echo e(__('admin.wallet_balance')); ?></th>
                <th><?php echo e(__('admin.accept_order')); ?></th>

                <th><?php echo e(__('admin.created_at')); ?></th>
                <th><?php echo e(__('admin.control')); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $rows; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr class="delete_provider">
                    <td>
                        <label class="container-checkbox">
                            <input type="checkbox" value="<?php echo e($row->id); ?>" name="delete_select" id="delete_select">
                            <span class="checkmark"></span>
                        </label>
                    </td>
                    <td><?php echo e($row->id); ?></td>

                    <td>
                        <?php if($row->provider && $row->provider->commercial_name): ?>
                            <?php echo e($row->provider->commercial_name); ?>

                        <?php else: ?>
                            <span class="text-muted"><?php echo e(__('admin.not_set')); ?></span>
                        <?php endif; ?>
                    </td>
                    <td><?php echo e($row->email ?? __('admin.not_set')); ?></td>
                    <td><?php echo e($row->full_phone); ?></td>
                    <td>
                        <?php if($row->is_active == 1): ?>
                            <span class="badge badge-success"><?php echo e(__('admin.active')); ?></span>
                        <?php else: ?>
                            <span class="badge badge-secondary"><?php echo e(__('admin.inactive')); ?></span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php echo e(__('admin.' . $row->provider?->status)); ?>

                    
                    </td>
                    <td><?php echo e(number_format($row->wallet_balance, 2)); ?> <?php echo e(__('admin.sar')); ?></td>
                <td>
                    <?php if($row->provider->accept_orders == 1): ?>
                    <span class="badge badge-success"><?php echo e(__('admin.yes')); ?></span>
                <?php else: ?>
                    <span class="badge badge-secondary"><?php echo e(__('admin.no')); ?></span>
                <?php endif; ?>
                </td>
                
                    <td><?php echo e($row->created_at->format('Y-m-d H:i')); ?></td>

                    <td class="row-action">

                        <span class="text-primary"><a href="<?php echo e(route('admin.providers.show', ['id' => $row->id])); ?>"
                                class="btn btn-warning btn-sm p-1" title="<?php echo e(__('admin.show')); ?>"><i class="feather icon-eye"></i></a></span>

                        <span class="action-edit text-primary"><a
                                href="<?php echo e(route('admin.providers.edit', ['id' => $row->id])); ?>"
                                class="btn btn-primary btn-sm p-1" title="<?php echo e(__('admin.edit')); ?>"><i
                                    class="feather icon-edit"></i></a></span>





                                    <span class="delete-row btn btn-danger btn-sm p-1" data-url="<?php echo e(url('admin/providers/' . $row->id)); ?>" title="<?php echo e(__('admin.delete')); ?>"><i class="feather icon-trash"></i></span>


                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>
    
    
    <?php if($rows->count() == 0): ?>
        <div class="d-flex flex-column w-100 align-center mt-4">
            <img src="<?php echo e(asset('admin/app-assets/images/pages/404.png')); ?>" alt="">
            <span class="mt-2" style="font-family: cairo"><?php echo e(__('admin.there_are_no_matches_matching')); ?></span>
        </div>
    <?php endif; ?>
    

</div>

<?php if($rows->count() > 0 && $rows instanceof \Illuminate\Pagination\AbstractPaginator): ?>
    <div class="d-flex justify-content-center mt-3">
        <?php echo e($rows->links()); ?>

    </div>
<?php endif; ?>

<?php /**PATH D:\Workstation\Taswk\sorriso-backend\resources\views/admin/providers/table.blade.php ENDPATH**/ ?>