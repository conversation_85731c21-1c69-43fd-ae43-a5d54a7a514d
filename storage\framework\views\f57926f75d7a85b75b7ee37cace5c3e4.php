<div class="position-relative">
    
    
    
    
    <table class="table " id="tab">
        <thead>
            <tr>
                <th>
                    <label class="container-checkbox">
                        <input type="checkbox" value="value1" name="name1" id="checkedAll">
                        <span class="checkmark"></span>
                    </label>
                </th>
                <th><?php echo e(__('admin.id_num')); ?></th>
                <th><?php echo e(__('admin.date')); ?></th>
                <th><?php echo e(__('admin.coupon_name')); ?></th>
                <th><?php echo e(__('admin.coupon_number')); ?></th>
                <th><?php echo e(__('admin.provider')); ?></th>
                <th><?php echo e(__('admin.discount_type')); ?></th>
                <th><?php echo e(__('admin.discount_value')); ?></th>
                <th><?php echo e(__('admin.start_date')); ?></th>
                <th><?php echo e(__('admin.expiry_date')); ?></th>
                <th><?php echo e(__('admin.used_times')); ?></th>
                <th><?php echo e(__('admin.expired')); ?></th>

                <th><?php echo e(__('admin.status')); ?></th>
                <th><?php echo e(__('admin.control')); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $coupons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $coupon): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        
                <tr class="delete_coupon">
                    <td class="text-center">
                        <label class="container-checkbox">
                            <input type="checkbox" class="checkSingle" id="<?php echo e($coupon->id); ?>">
                            <span class="checkmark"></span>
                        </label>
                    </td>
                    <td><?php echo e($coupon->id); ?></td>
                    <td><?php echo e(\Carbon\Carbon::parse($coupon->created_at)->format('d/m/Y')); ?></td>
                    <td><?php echo e($coupon->coupon_name ?? __('admin.not_set')); ?></td>
                    <td><?php echo e($coupon->coupon_num); ?></td>
                    <td>
                        <span class="badge badge-info"><?php echo e($coupon->provider->commercial_name ?? __('admin.provider_not_found')); ?></span>
                    </td>
                    <td>
                        <span class="badge badge-<?php echo e($coupon->type == 'ratio' ? 'warning' : 'primary'); ?>">
                            <?php echo e($coupon->type == 'ratio' ? __('admin.percentage') : __('admin.fixed_number')); ?>

                        </span>
                    </td>
                    <td>
                        <?php echo e($coupon->discount); ?><?php echo e($coupon->type == 'ratio' ? '%' : ''); ?>

                        <?php if($coupon->max_discount && $coupon->type == 'ratio'): ?>
                            <small class="text-muted">(<?php echo e(__('admin.max')); ?>: <?php echo e($coupon->max_discount); ?>)</small>
                        <?php endif; ?>
                    </td>
                   
                    <td><?php echo e($coupon->start_date ? date('d-m-Y', strtotime($coupon->start_date)) : __('admin.not_set')); ?></td>
                    <td><?php echo e($coupon->expire_date ? date('d-m-Y', strtotime($coupon->expire_date)) : __('admin.no_expiry')); ?></td>
                    <td>
                        <a href="<?php echo e(route('admin.coupons.orders', ['coupon' => $coupon->id])); ?>">
                            <?php echo e($coupon->usage_time); ?>

                        </a>
                    </td>
                  
                    <td>
                        <?php if($coupon->is_valid): ?>

                            <span class="badge badge-success"><?php echo e(__('admin.valid')); ?></span>
                        <?php else: ?>
                            <span class="badge badge-danger"><?php echo e(__('admin.expired')); ?></span>
                        <?php endif; ?>
                    </td>
                    
                    
                    <td>
                        <?php echo toggleBooleanView($coupon , route('admin.model.active' , ['model' =>'Coupon' , 'id' => $coupon->id , 'action' => 'is_active'])); ?>

                    </td>

                    <td class="product-action">
                        <span class="text-primary"><a href="<?php echo e(route('admin.coupons.show', ['id' => $coupon->id])); ?>" class="btn btn-warning btn-sm p-1" title="<?php echo e(__('admin.show')); ?>"><i class="feather icon-eye"></i></a></span>
                        <span class="action-edit text-primary"><a href="<?php echo e(route('admin.coupons.edit', ['id' => $coupon->id])); ?>" class="btn btn-primary btn-sm p-1" title="<?php echo e(__('admin.edit')); ?>"><i class="feather icon-edit"></i></a></span>
                        <span class="delete-row btn btn-danger btn-sm p-1" data-url="<?php echo e(url('admin/coupons/' . $coupon->id)); ?>" title="<?php echo e(__('admin.delete')); ?>"><i class="feather icon-trash"></i></span>
                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>
    
    
    <?php if($coupons->count() == 0): ?>
        <div class="d-flex flex-column w-100 align-center mt-4">
            <img src="<?php echo e(asset('admin/app-assets/images/pages/404.png')); ?>" alt="">
            <span class="mt-2" style="font-family: cairo"><?php echo e(__('admin.there_are_no_matches_matching')); ?></span>
        </div>
    <?php endif; ?>
    

</div>

<?php if($coupons->count() > 0 && $coupons instanceof \Illuminate\Pagination\AbstractPaginator ): ?>
    <div class="d-flex justify-content-center mt-3">
        <?php echo e($coupons->links()); ?>

    </div>
<?php endif; ?>
<?php /**PATH D:\Workstation\Taswk\sorriso-backend\resources\views/admin/coupons/table.blade.php ENDPATH**/ ?>