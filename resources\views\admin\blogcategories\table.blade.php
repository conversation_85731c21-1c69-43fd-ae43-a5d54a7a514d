<div class="position-relative">
    {{-- table loader  --}}
    {{-- <div class="table_loader" >
        {{__('admin.loading')}}
    </div> --}}
    {{-- table loader  --}}
    
    {{-- table content --}}
    <table class="table " id="tab">
        <thead>
            <tr>
                <th>
                    <label class="container-checkbox">
                        <input type="checkbox" value="value1" name="name1" id="checkedAll">
                        <span class="checkmark"></span>
                    </label>
                </th>
                <th>{{__('admin.created_at')}}</th>
                <th>{{__('admin.name')}}</th>
                <th>{{__('admin.blogs_count')}}</th>
                <th>{{__('admin.status')}}</th>
                <th>{{__('admin.control')}}</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($blogcategories as $blogcategory)
                <tr class="delete_row">
                    <td class="text-center">
                        <label class="container-checkbox">
                        <input type="checkbox" class="checkSingle" id="{{ $blogcategory->id }}">
                        <span class="checkmark"></span>
                        </label>
                    </td>
                    <td>{{ $blogcategory->created_at->format('Y-m-d H:i:s') }}</td>
                    <td>{{ $blogcategory->getTranslations('name')[app()->getLocale()] ?? $blogcategory->getTranslations('name')[array_key_first($blogcategory->getTranslations('name'))] }}</td>
                    <td>{{ $blogcategory->blogs_count }}</td>
                    <td>
                        {!! toggleBooleanView($blogcategory , route('admin.model.active' , ['model' =>'BlogCategory' , 'id' => $blogcategory->id , 'action' => 'is_active'])) !!}
                    </td>
                    
                    <td class="product-action">
                        <span class="text-primary"><a href="{{ route('admin.blogcategories.show', ['id' => $blogcategory->id]) }}" class="btn btn-warning btn-sm p-1" title="{{ __('admin.show') }}"><i class="feather icon-eye"></i></a></span>
                        <span class="action-edit text-primary"><a href="{{ route('admin.blogcategories.edit', ['id' => $blogcategory->id]) }}" class="btn btn-primary btn-sm p-1" title="{{ __('admin.edit') }}"><i class="feather icon-edit"></i></a></span>
                        <span class="delete-row btn btn-danger btn-sm p-1" data-url="{{ url('admin/blogcategories/' . $blogcategory->id) }}" title="{{ __('admin.delete') }}"><i class="feather icon-trash"></i></span>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
    {{-- table content --}}
    {{-- no data found div --}}
    @if ($blogcategories->count() == 0)
        <div class="d-flex flex-column w-100 align-center mt-4">
            <img src="{{asset('admin/app-assets/images/pages/404.png')}}" alt="">
            <span class="mt-2" style="font-family: cairo">{{__('admin.there_are_no_matches_matching')}}</span>
        </div>
    @endif
    {{-- no data found div --}}

</div>
{{-- pagination  links div --}}
@if ($blogcategories->count() > 0 && $blogcategories instanceof \Illuminate\Pagination\AbstractPaginator )
    <div class="d-flex justify-content-center mt-3">
        {{$blogcategories->links()}}
    </div>
@endif
{{-- pagination  links div --}}

