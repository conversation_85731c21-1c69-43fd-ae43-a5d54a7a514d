<div class="position-relative">
    
    
    

    
 <table class="table" id="tab">
    <thead>
        <tr>
            <th><?php echo e(__('admin.order_number')); ?></th>
            <th><?php echo e(__('admin.user')); ?></th>
            <th><?php echo e(__('admin.cancel_reason')); ?></th>
            <th><?php echo e(__('admin.payment_method')); ?></th>
            <th><?php echo e(__('admin.total_amount')); ?></th>
            <th><?php echo e(__('admin.request_date')); ?></th>
            <th><?php echo e(__('admin.control')); ?></th>
        </tr>
    </thead>
    <tbody>
        <?php $__currentLoopData = $orders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tr>
                <td>
                    <span class="badge badge-warning"><?php echo e($order->order_number ?? $order->order_num); ?></span>
                </td>
                <td>
                    <?php if($order->user): ?>
                        <div>
                            <strong><?php echo e($order->user->name); ?></strong><br>
                            <small class="text-muted"><?php echo e($order->user->phone); ?></small>
                        </div>
                    <?php else: ?>
                        <span class="text-muted">-</span>
                    <?php endif; ?>
                </td>
                <td>
                    <?php if($order->cancelReason): ?>
                        <?php
                            $reasonData = json_decode($order->cancelReason->reason, true);
                            $reasonText = $reasonData[app()->getLocale()] ?? $reasonData['en'] ?? 'Unknown';
                        ?>
                        <span class="badge badge-secondary" title="<?php echo e($reasonText); ?>">
                            <?php echo e(Str::limit($reasonText, 30)); ?>

                        </span>
                    <?php else: ?>
                        <span class="text-muted">-</span>
                    <?php endif; ?>
                </td>
                <td>
                    <?php if($order->paymentMethod): ?>
                        <div class="d-flex align-items-center">
                            <?php if($order->payment_method_id == 1): ?>
                                <i class="feather icon-credit-card text-info mr-1"></i>
                            <?php elseif($order->payment_method_id == 5): ?>
                                <i class="feather icon-send text-primary mr-1"></i>
                            <?php else: ?>
                                <i class="feather icon-credit-card text-secondary mr-1"></i>
                            <?php endif; ?>
                            <span><?php echo e($order->paymentMethod->name); ?></span>
                        </div>
                    <?php else: ?>
                        <span class="text-muted">-</span>
                    <?php endif; ?>
                </td>
                <td>
                    <div>
                        <strong class="text-success"><?php echo e(number_format($order->total ?? $order->final_total, 2)); ?> <?php echo e(__('admin.sar')); ?></strong>
                    </div>
                    <?php if($order->services_total > 0 || $order->products_total > 0): ?>
                        <small class="text-muted">
                            <?php if($order->services_total > 0): ?>
                                <?php echo e(__('admin.services')); ?>: <?php echo e(number_format($order->services_total, 2)); ?>

                            <?php endif; ?>
                            <?php if($order->products_total > 0): ?>
                                <?php if($order->services_total > 0): ?> | <?php endif; ?>
                                <?php echo e(__('admin.products')); ?>: <?php echo e(number_format($order->products_total, 2)); ?>

                            <?php endif; ?>
                        </small>
                    <?php endif; ?>
                </td>
                <td>
                    <div><?php echo e($order->updated_at->format('d/m/Y')); ?></div>
                    <small class="text-muted"><?php echo e($order->updated_at->format('H:i')); ?></small>
                    <br>
                    <small class="text-warning">
                        <i class="feather icon-clock mr-1"></i>
                        <?php echo e($order->updated_at->diffForHumans()); ?>

                    </small>
                </td>
                <td class="order-action">
                    <div class="btn-group" role="group">
                        <a href="<?php echo e(route('admin.cancel_request_orders.show', ['id' => $order->id])); ?>"
                           class="btn btn-info btn-sm p-1" title="<?php echo e(__('admin.view_details')); ?>">
                            <i class="feather icon-eye"></i>
                        </a>

                        <?php
                            $cancelReasonText = __('admin.no_reason_provided');
                            if($order->cancelReason) {
                                $reasonData = json_decode($order->cancelReason->reason, true);
                                $cancelReasonText = $reasonData[app()->getLocale()] ?? $reasonData['en'] ?? 'Unknown';
                            }
                        ?>

                    </div>
                </td>
            </tr>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </tbody>
</table>


    
    <?php if($orders->count() == 0): ?>
        <div class="d-flex flex-column w-100 align-center mt-4">
            <img src="<?php echo e(asset('admin/app-assets/images/pages/404.png')); ?>" alt="">
            <span class="mt-2" style="font-family: cairo"><?php echo e(__('admin.no_cancel_requests_found')); ?></span>
        </div>
    <?php endif; ?>
    

</div>


<?php if($orders->count() > 0 && $orders instanceof \Illuminate\Pagination\AbstractPaginator ): ?>
    <div class="d-flex justify-content-center mt-3">
        <?php echo e($orders->links()); ?>

    </div>
<?php endif; ?>

<?php /**PATH D:\Workstation\Taswk\sorriso-backend\resources\views/admin/cancel_request_orders/table.blade.php ENDPATH**/ ?>