[2025-07-29 16:08:24] local.INFO: MyFatoorah unified payment webhook received {"paymentId":"**************715174","Id":"**************715174"} 
[2025-07-29 16:08:26] local.INFO: Payment details fetched successfully {"payment_id":"**************715174","response":{"InvoiceId":5981526,"InvoiceStatus":"Paid","InvoiceReference":"**********","CustomerReference":"4","CreatedDate":"2025-07-29T16:04:18.52","ExpiryDate":"August 1, 2025","ExpiryTime":"16:04:18.520","InvoiceValue":30.5,"Comments":null,"CustomerName":"dqwee31232","CustomerMobile":"+************","CustomerEmail":"<EMAIL>","UserDefinedField":"order_payment","InvoiceDisplayValue":"30.500 SR","DueDeposit":29.448,"DepositStatus":"Not Deposited","InvoiceItems":[{"ItemName":"شامبو شعر احترافي","Quantity":3,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"شامبو شعر احترافي","Quantity":1,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Delivery Fee","Quantity":1,"UnitPrice":15,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Total Adjustment","Quantity":1,"UnitPrice":-46.5,"Weight":null,"Width":null,"Height":null,"Depth":null}],"InvoiceTransactions":[{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}],"Suppliers":[],"InvoiceError":"","focusTransaction":{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}}} 
[2025-07-29 16:08:26] local.INFO: Payment details fetched from MyFatoorah {"payment_id":"**************715174","status":"Paid","entity_id":"4","payment_type":"order_payment"} 
[2025-07-29 16:08:26] local.INFO: Processing successful order payment status change {"payment_id":"**************715174","entity_id":"4"} 
[2025-07-29 16:08:28] local.ERROR: MyFatoorah order payment verification failed {"payment_id":"**************715174","order_id":"4","error":"Order not found"} 
[2025-07-29 16:08:28] local.WARNING: order payment webhook verification failed {"payment_id":"**************715174","entity_id":"4","type":"order","error":"Payment verification failed: Order not found"} 
[2025-07-29 16:09:07] local.INFO: MyFatoorah unified payment webhook received {"paymentId":"**************715174","Id":"**************715174"} 
[2025-07-29 16:09:10] local.INFO: Payment details fetched successfully {"payment_id":"**************715174","response":{"InvoiceId":5981526,"InvoiceStatus":"Paid","InvoiceReference":"**********","CustomerReference":"4","CreatedDate":"2025-07-29T16:04:18.52","ExpiryDate":"August 1, 2025","ExpiryTime":"16:04:18.520","InvoiceValue":30.5,"Comments":null,"CustomerName":"dqwee31232","CustomerMobile":"+************","CustomerEmail":"<EMAIL>","UserDefinedField":"order_payment","InvoiceDisplayValue":"30.500 SR","DueDeposit":29.448,"DepositStatus":"Not Deposited","InvoiceItems":[{"ItemName":"شامبو شعر احترافي","Quantity":3,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"شامبو شعر احترافي","Quantity":1,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Delivery Fee","Quantity":1,"UnitPrice":15,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Total Adjustment","Quantity":1,"UnitPrice":-46.5,"Weight":null,"Width":null,"Height":null,"Depth":null}],"InvoiceTransactions":[{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}],"Suppliers":[],"InvoiceError":"","focusTransaction":{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}}} 
[2025-07-29 16:09:10] local.INFO: Payment details fetched from MyFatoorah {"payment_id":"**************715174","status":"Paid","entity_id":"4","payment_type":"order_payment"} 
[2025-07-29 16:09:10] local.INFO: Processing successful order payment status change {"payment_id":"**************715174","entity_id":"4"} 
[2025-07-29 16:09:11] local.ERROR: MyFatoorah order payment verification failed {"payment_id":"**************715174","order_id":"4","error":"Attempt to read property \"providerSubOrders\" on null"} 
[2025-07-29 16:09:11] local.WARNING: order payment webhook verification failed {"payment_id":"**************715174","entity_id":"4","type":"order","error":"Payment verification failed: Attempt to read property \"providerSubOrders\" on null"} 
[2025-07-29 16:09:49] local.INFO: MyFatoorah unified payment webhook received {"paymentId":"**************715174","Id":"**************715174"} 
[2025-07-29 16:09:50] local.INFO: Payment details fetched successfully {"payment_id":"**************715174","response":{"InvoiceId":5981526,"InvoiceStatus":"Paid","InvoiceReference":"**********","CustomerReference":"4","CreatedDate":"2025-07-29T16:04:18.52","ExpiryDate":"August 1, 2025","ExpiryTime":"16:04:18.520","InvoiceValue":30.5,"Comments":null,"CustomerName":"dqwee31232","CustomerMobile":"+************","CustomerEmail":"<EMAIL>","UserDefinedField":"order_payment","InvoiceDisplayValue":"30.500 SR","DueDeposit":29.448,"DepositStatus":"Not Deposited","InvoiceItems":[{"ItemName":"شامبو شعر احترافي","Quantity":3,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"شامبو شعر احترافي","Quantity":1,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Delivery Fee","Quantity":1,"UnitPrice":15,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Total Adjustment","Quantity":1,"UnitPrice":-46.5,"Weight":null,"Width":null,"Height":null,"Depth":null}],"InvoiceTransactions":[{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}],"Suppliers":[],"InvoiceError":"","focusTransaction":{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}}} 
[2025-07-29 16:09:50] local.INFO: Payment details fetched from MyFatoorah {"payment_id":"**************715174","status":"Paid","entity_id":"4","payment_type":"order_payment"} 
[2025-07-29 16:09:50] local.INFO: Processing successful order payment status change {"payment_id":"**************715174","entity_id":"4"} 
[2025-07-29 16:09:51] local.ERROR: MyFatoorah order payment verification failed {"payment_id":"**************715174","order_id":"4","error":"Method Illuminate\\Database\\Eloquent\\Collection::withTrashed does not exist."} 
[2025-07-29 16:09:51] local.WARNING: order payment webhook verification failed {"payment_id":"**************715174","entity_id":"4","type":"order","error":"Payment verification failed: Method Illuminate\\Database\\Eloquent\\Collection::withTrashed does not exist."} 
[2025-07-29 16:10:36] local.INFO: MyFatoorah unified payment webhook received {"paymentId":"**************715174","Id":"**************715174"} 
[2025-07-29 16:10:37] local.INFO: Payment details fetched successfully {"payment_id":"**************715174","response":{"InvoiceId":5981526,"InvoiceStatus":"Paid","InvoiceReference":"**********","CustomerReference":"4","CreatedDate":"2025-07-29T16:04:18.52","ExpiryDate":"August 1, 2025","ExpiryTime":"16:04:18.520","InvoiceValue":30.5,"Comments":null,"CustomerName":"dqwee31232","CustomerMobile":"+************","CustomerEmail":"<EMAIL>","UserDefinedField":"order_payment","InvoiceDisplayValue":"30.500 SR","DueDeposit":29.448,"DepositStatus":"Not Deposited","InvoiceItems":[{"ItemName":"شامبو شعر احترافي","Quantity":3,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"شامبو شعر احترافي","Quantity":1,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Delivery Fee","Quantity":1,"UnitPrice":15,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Total Adjustment","Quantity":1,"UnitPrice":-46.5,"Weight":null,"Width":null,"Height":null,"Depth":null}],"InvoiceTransactions":[{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}],"Suppliers":[],"InvoiceError":"","focusTransaction":{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}}} 
[2025-07-29 16:10:37] local.INFO: Payment details fetched from MyFatoorah {"payment_id":"**************715174","status":"Paid","entity_id":"4","payment_type":"order_payment"} 
[2025-07-29 16:10:37] local.INFO: Processing successful order payment status change {"payment_id":"**************715174","entity_id":"4"} 
[2025-07-29 16:11:07] local.INFO: MyFatoorah unified payment webhook received {"paymentId":"**************715174","Id":"**************715174"} 
[2025-07-29 16:11:09] local.INFO: Payment details fetched successfully {"payment_id":"**************715174","response":{"InvoiceId":5981526,"InvoiceStatus":"Paid","InvoiceReference":"**********","CustomerReference":"4","CreatedDate":"2025-07-29T16:04:18.52","ExpiryDate":"August 1, 2025","ExpiryTime":"16:04:18.520","InvoiceValue":30.5,"Comments":null,"CustomerName":"dqwee31232","CustomerMobile":"+************","CustomerEmail":"<EMAIL>","UserDefinedField":"order_payment","InvoiceDisplayValue":"30.500 SR","DueDeposit":29.448,"DepositStatus":"Not Deposited","InvoiceItems":[{"ItemName":"شامبو شعر احترافي","Quantity":3,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"شامبو شعر احترافي","Quantity":1,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Delivery Fee","Quantity":1,"UnitPrice":15,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Total Adjustment","Quantity":1,"UnitPrice":-46.5,"Weight":null,"Width":null,"Height":null,"Depth":null}],"InvoiceTransactions":[{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}],"Suppliers":[],"InvoiceError":"","focusTransaction":{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}}} 
[2025-07-29 16:11:09] local.INFO: Payment details fetched from MyFatoorah {"payment_id":"**************715174","status":"Paid","entity_id":"4","payment_type":"order_payment"} 
[2025-07-29 16:11:09] local.INFO: Processing successful order payment status change {"payment_id":"**************715174","entity_id":"4"} 
[2025-07-29 16:11:24] local.INFO: MyFatoorah unified payment webhook received {"paymentId":"**************715174","Id":"**************715174"} 
[2025-07-29 16:11:25] local.INFO: Payment details fetched successfully {"payment_id":"**************715174","response":{"InvoiceId":5981526,"InvoiceStatus":"Paid","InvoiceReference":"**********","CustomerReference":"4","CreatedDate":"2025-07-29T16:04:18.52","ExpiryDate":"August 1, 2025","ExpiryTime":"16:04:18.520","InvoiceValue":30.5,"Comments":null,"CustomerName":"dqwee31232","CustomerMobile":"+************","CustomerEmail":"<EMAIL>","UserDefinedField":"order_payment","InvoiceDisplayValue":"30.500 SR","DueDeposit":29.448,"DepositStatus":"Not Deposited","InvoiceItems":[{"ItemName":"شامبو شعر احترافي","Quantity":3,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"شامبو شعر احترافي","Quantity":1,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Delivery Fee","Quantity":1,"UnitPrice":15,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Total Adjustment","Quantity":1,"UnitPrice":-46.5,"Weight":null,"Width":null,"Height":null,"Depth":null}],"InvoiceTransactions":[{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}],"Suppliers":[],"InvoiceError":"","focusTransaction":{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}}} 
[2025-07-29 16:11:25] local.INFO: Payment details fetched from MyFatoorah {"payment_id":"**************715174","status":"Paid","entity_id":"4","payment_type":"order_payment"} 
[2025-07-29 16:11:25] local.INFO: Processing successful order payment status change {"payment_id":"**************715174","entity_id":"4"} 
[2025-07-29 16:11:25] local.ERROR: MyFatoorah order payment verification failed {"payment_id":"**************715174","order_id":"4","error":"Attempt to read property \"providerSubOrders\" on null"} 
[2025-07-29 16:11:25] local.WARNING: order payment webhook verification failed {"payment_id":"**************715174","entity_id":"4","type":"order","error":"Payment verification failed: Attempt to read property \"providerSubOrders\" on null"} 
[2025-07-29 16:14:03] local.INFO: MyFatoorah unified payment webhook received {"paymentId":"**************715174","Id":"**************715174"} 
[2025-07-29 16:14:04] local.INFO: Payment details fetched successfully {"payment_id":"**************715174","response":{"InvoiceId":5981526,"InvoiceStatus":"Paid","InvoiceReference":"**********","CustomerReference":"4","CreatedDate":"2025-07-29T16:04:18.52","ExpiryDate":"August 1, 2025","ExpiryTime":"16:04:18.520","InvoiceValue":30.5,"Comments":null,"CustomerName":"dqwee31232","CustomerMobile":"+************","CustomerEmail":"<EMAIL>","UserDefinedField":"order_payment","InvoiceDisplayValue":"30.500 SR","DueDeposit":29.448,"DepositStatus":"Not Deposited","InvoiceItems":[{"ItemName":"شامبو شعر احترافي","Quantity":3,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"شامبو شعر احترافي","Quantity":1,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Delivery Fee","Quantity":1,"UnitPrice":15,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Total Adjustment","Quantity":1,"UnitPrice":-46.5,"Weight":null,"Width":null,"Height":null,"Depth":null}],"InvoiceTransactions":[{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}],"Suppliers":[],"InvoiceError":"","focusTransaction":{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}}} 
[2025-07-29 16:14:04] local.INFO: Payment details fetched from MyFatoorah {"payment_id":"**************715174","status":"Paid","entity_id":"4","payment_type":"order_payment"} 
[2025-07-29 16:14:04] local.INFO: Processing successful order payment status change {"payment_id":"**************715174","entity_id":"4"} 
[2025-07-29 16:14:05] local.INFO: Order found for MyFatoorah callback {"order_id":4,"order_number":"7309825","is_trashed":true,"payment_status":"pending"} 
[2025-07-29 16:14:05] local.ERROR: MyFatoorah order payment verification failed {"payment_id":"**************715174","order_id":"4","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'field list' (Connection: mysql, SQL: update `orders` set `payment_status` = paid, `payment_reference` = **************715174, `status` = processing, `orders`.`updated_at` = 2025-07-29 16:14:05 where `id` = 4)"} 
[2025-07-29 16:14:05] local.WARNING: order payment webhook verification failed {"payment_id":"**************715174","entity_id":"4","type":"order","error":"Payment verification failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'field list' (Connection: mysql, SQL: update `orders` set `payment_status` = paid, `payment_reference` = **************715174, `status` = processing, `orders`.`updated_at` = 2025-07-29 16:14:05 where `id` = 4)"} 
[2025-07-29 16:15:37] local.INFO: MyFatoorah unified payment webhook received {"paymentId":"**************715174","Id":"**************715174"} 
[2025-07-29 16:15:39] local.INFO: Payment details fetched successfully {"payment_id":"**************715174","response":{"InvoiceId":5981526,"InvoiceStatus":"Paid","InvoiceReference":"**********","CustomerReference":"4","CreatedDate":"2025-07-29T16:04:18.52","ExpiryDate":"August 1, 2025","ExpiryTime":"16:04:18.520","InvoiceValue":30.5,"Comments":null,"CustomerName":"dqwee31232","CustomerMobile":"+************","CustomerEmail":"<EMAIL>","UserDefinedField":"order_payment","InvoiceDisplayValue":"30.500 SR","DueDeposit":29.448,"DepositStatus":"Not Deposited","InvoiceItems":[{"ItemName":"شامبو شعر احترافي","Quantity":3,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"شامبو شعر احترافي","Quantity":1,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Delivery Fee","Quantity":1,"UnitPrice":15,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Total Adjustment","Quantity":1,"UnitPrice":-46.5,"Weight":null,"Width":null,"Height":null,"Depth":null}],"InvoiceTransactions":[{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}],"Suppliers":[],"InvoiceError":"","focusTransaction":{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}}} 
[2025-07-29 16:15:39] local.INFO: Payment details fetched from MyFatoorah {"payment_id":"**************715174","status":"Paid","entity_id":"4","payment_type":"order_payment"} 
[2025-07-29 16:15:39] local.INFO: Processing successful order payment status change {"payment_id":"**************715174","entity_id":"4"} 
[2025-07-29 16:15:40] local.INFO: Order found for MyFatoorah callback {"order_id":4,"order_number":"7309825","is_trashed":true,"payment_status":"pending"} 
[2025-07-29 16:15:40] local.ERROR: MyFatoorah order payment verification failed {"payment_id":"**************715174","order_id":"4","error":"SQLSTATE[01000]: Warning: 1265 Data truncated for column 'payment_status' at row 1 (Connection: mysql, SQL: update `orders` set `current_status` = processing, `payment_status` = paid, `payment_reference` = **************715174, `orders`.`updated_at` = 2025-07-29 16:15:40 where `id` = 4)"} 
[2025-07-29 16:15:40] local.WARNING: order payment webhook verification failed {"payment_id":"**************715174","entity_id":"4","type":"order","error":"Payment verification failed: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'payment_status' at row 1 (Connection: mysql, SQL: update `orders` set `current_status` = processing, `payment_status` = paid, `payment_reference` = **************715174, `orders`.`updated_at` = 2025-07-29 16:15:40 where `id` = 4)"} 
[2025-07-29 16:16:05] local.INFO: MyFatoorah unified payment webhook received {"paymentId":"**************715174","Id":"**************715174"} 
[2025-07-29 16:16:07] local.INFO: Payment details fetched successfully {"payment_id":"**************715174","response":{"InvoiceId":5981526,"InvoiceStatus":"Paid","InvoiceReference":"**********","CustomerReference":"4","CreatedDate":"2025-07-29T16:04:18.52","ExpiryDate":"August 1, 2025","ExpiryTime":"16:04:18.520","InvoiceValue":30.5,"Comments":null,"CustomerName":"dqwee31232","CustomerMobile":"+************","CustomerEmail":"<EMAIL>","UserDefinedField":"order_payment","InvoiceDisplayValue":"30.500 SR","DueDeposit":29.448,"DepositStatus":"Not Deposited","InvoiceItems":[{"ItemName":"شامبو شعر احترافي","Quantity":3,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"شامبو شعر احترافي","Quantity":1,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Delivery Fee","Quantity":1,"UnitPrice":15,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Total Adjustment","Quantity":1,"UnitPrice":-46.5,"Weight":null,"Width":null,"Height":null,"Depth":null}],"InvoiceTransactions":[{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}],"Suppliers":[],"InvoiceError":"","focusTransaction":{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}}} 
[2025-07-29 16:16:07] local.INFO: Payment details fetched from MyFatoorah {"payment_id":"**************715174","status":"Paid","entity_id":"4","payment_type":"order_payment"} 
[2025-07-29 16:16:07] local.INFO: Processing successful order payment status change {"payment_id":"**************715174","entity_id":"4"} 
[2025-07-29 16:16:09] local.INFO: Order found for MyFatoorah callback {"order_id":4,"order_number":"7309825","is_trashed":true,"payment_status":"pending"} 
[2025-07-29 16:16:09] local.INFO: Cart cleared after payment confirmation {"order_id":4,"order_number":"7309825","user_id":28} 
[2025-07-29 16:16:09] local.INFO: Order payment successful {"order_id":4,"order_number":"7309825","payment_id":"**************715174","amount":"30.50","cart_cleared":true} 
[2025-07-29 16:20:24] local.ERROR: Command "check-notifications" is not defined.

Did you mean one of these?
    notifications:table
    orders:check-notifications {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"check-notifications\" is not defined.

Did you mean one of these?
    notifications:table
    orders:check-notifications at D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\symfony\\console\\Application.php:725)
[stacktrace]
#0 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find('check-notificat...')
#1 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\Workstation\\Taswk\\sorriso-backend\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 {main}
"} 
