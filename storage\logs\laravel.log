[2025-07-29 16:08:24] local.INFO: MyFatoorah unified payment webhook received {"paymentId":"**************715174","Id":"**************715174"} 
[2025-07-29 16:08:26] local.INFO: Payment details fetched successfully {"payment_id":"**************715174","response":{"InvoiceId":5981526,"InvoiceStatus":"Paid","InvoiceReference":"**********","CustomerReference":"4","CreatedDate":"2025-07-29T16:04:18.52","ExpiryDate":"August 1, 2025","ExpiryTime":"16:04:18.520","InvoiceValue":30.5,"Comments":null,"CustomerName":"dqwee31232","CustomerMobile":"+************","CustomerEmail":"<EMAIL>","UserDefinedField":"order_payment","InvoiceDisplayValue":"30.500 SR","DueDeposit":29.448,"DepositStatus":"Not Deposited","InvoiceItems":[{"ItemName":"شامبو شعر احترافي","Quantity":3,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"شامبو شعر احترافي","Quantity":1,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Delivery Fee","Quantity":1,"UnitPrice":15,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Total Adjustment","Quantity":1,"UnitPrice":-46.5,"Weight":null,"Width":null,"Height":null,"Depth":null}],"InvoiceTransactions":[{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}],"Suppliers":[],"InvoiceError":"","focusTransaction":{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}}} 
[2025-07-29 16:08:26] local.INFO: Payment details fetched from MyFatoorah {"payment_id":"**************715174","status":"Paid","entity_id":"4","payment_type":"order_payment"} 
[2025-07-29 16:08:26] local.INFO: Processing successful order payment status change {"payment_id":"**************715174","entity_id":"4"} 
[2025-07-29 16:08:28] local.ERROR: MyFatoorah order payment verification failed {"payment_id":"**************715174","order_id":"4","error":"Order not found"} 
[2025-07-29 16:08:28] local.WARNING: order payment webhook verification failed {"payment_id":"**************715174","entity_id":"4","type":"order","error":"Payment verification failed: Order not found"} 
[2025-07-29 16:09:07] local.INFO: MyFatoorah unified payment webhook received {"paymentId":"**************715174","Id":"**************715174"} 
[2025-07-29 16:09:10] local.INFO: Payment details fetched successfully {"payment_id":"**************715174","response":{"InvoiceId":5981526,"InvoiceStatus":"Paid","InvoiceReference":"**********","CustomerReference":"4","CreatedDate":"2025-07-29T16:04:18.52","ExpiryDate":"August 1, 2025","ExpiryTime":"16:04:18.520","InvoiceValue":30.5,"Comments":null,"CustomerName":"dqwee31232","CustomerMobile":"+************","CustomerEmail":"<EMAIL>","UserDefinedField":"order_payment","InvoiceDisplayValue":"30.500 SR","DueDeposit":29.448,"DepositStatus":"Not Deposited","InvoiceItems":[{"ItemName":"شامبو شعر احترافي","Quantity":3,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"شامبو شعر احترافي","Quantity":1,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Delivery Fee","Quantity":1,"UnitPrice":15,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Total Adjustment","Quantity":1,"UnitPrice":-46.5,"Weight":null,"Width":null,"Height":null,"Depth":null}],"InvoiceTransactions":[{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}],"Suppliers":[],"InvoiceError":"","focusTransaction":{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}}} 
[2025-07-29 16:09:10] local.INFO: Payment details fetched from MyFatoorah {"payment_id":"**************715174","status":"Paid","entity_id":"4","payment_type":"order_payment"} 
[2025-07-29 16:09:10] local.INFO: Processing successful order payment status change {"payment_id":"**************715174","entity_id":"4"} 
[2025-07-29 16:09:11] local.ERROR: MyFatoorah order payment verification failed {"payment_id":"**************715174","order_id":"4","error":"Attempt to read property \"providerSubOrders\" on null"} 
[2025-07-29 16:09:11] local.WARNING: order payment webhook verification failed {"payment_id":"**************715174","entity_id":"4","type":"order","error":"Payment verification failed: Attempt to read property \"providerSubOrders\" on null"} 
[2025-07-29 16:09:49] local.INFO: MyFatoorah unified payment webhook received {"paymentId":"**************715174","Id":"**************715174"} 
[2025-07-29 16:09:50] local.INFO: Payment details fetched successfully {"payment_id":"**************715174","response":{"InvoiceId":5981526,"InvoiceStatus":"Paid","InvoiceReference":"**********","CustomerReference":"4","CreatedDate":"2025-07-29T16:04:18.52","ExpiryDate":"August 1, 2025","ExpiryTime":"16:04:18.520","InvoiceValue":30.5,"Comments":null,"CustomerName":"dqwee31232","CustomerMobile":"+************","CustomerEmail":"<EMAIL>","UserDefinedField":"order_payment","InvoiceDisplayValue":"30.500 SR","DueDeposit":29.448,"DepositStatus":"Not Deposited","InvoiceItems":[{"ItemName":"شامبو شعر احترافي","Quantity":3,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"شامبو شعر احترافي","Quantity":1,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Delivery Fee","Quantity":1,"UnitPrice":15,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Total Adjustment","Quantity":1,"UnitPrice":-46.5,"Weight":null,"Width":null,"Height":null,"Depth":null}],"InvoiceTransactions":[{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}],"Suppliers":[],"InvoiceError":"","focusTransaction":{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}}} 
[2025-07-29 16:09:50] local.INFO: Payment details fetched from MyFatoorah {"payment_id":"**************715174","status":"Paid","entity_id":"4","payment_type":"order_payment"} 
[2025-07-29 16:09:50] local.INFO: Processing successful order payment status change {"payment_id":"**************715174","entity_id":"4"} 
[2025-07-29 16:09:51] local.ERROR: MyFatoorah order payment verification failed {"payment_id":"**************715174","order_id":"4","error":"Method Illuminate\\Database\\Eloquent\\Collection::withTrashed does not exist."} 
[2025-07-29 16:09:51] local.WARNING: order payment webhook verification failed {"payment_id":"**************715174","entity_id":"4","type":"order","error":"Payment verification failed: Method Illuminate\\Database\\Eloquent\\Collection::withTrashed does not exist."} 
[2025-07-29 16:10:36] local.INFO: MyFatoorah unified payment webhook received {"paymentId":"**************715174","Id":"**************715174"} 
[2025-07-29 16:10:37] local.INFO: Payment details fetched successfully {"payment_id":"**************715174","response":{"InvoiceId":5981526,"InvoiceStatus":"Paid","InvoiceReference":"**********","CustomerReference":"4","CreatedDate":"2025-07-29T16:04:18.52","ExpiryDate":"August 1, 2025","ExpiryTime":"16:04:18.520","InvoiceValue":30.5,"Comments":null,"CustomerName":"dqwee31232","CustomerMobile":"+************","CustomerEmail":"<EMAIL>","UserDefinedField":"order_payment","InvoiceDisplayValue":"30.500 SR","DueDeposit":29.448,"DepositStatus":"Not Deposited","InvoiceItems":[{"ItemName":"شامبو شعر احترافي","Quantity":3,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"شامبو شعر احترافي","Quantity":1,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Delivery Fee","Quantity":1,"UnitPrice":15,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Total Adjustment","Quantity":1,"UnitPrice":-46.5,"Weight":null,"Width":null,"Height":null,"Depth":null}],"InvoiceTransactions":[{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}],"Suppliers":[],"InvoiceError":"","focusTransaction":{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}}} 
[2025-07-29 16:10:37] local.INFO: Payment details fetched from MyFatoorah {"payment_id":"**************715174","status":"Paid","entity_id":"4","payment_type":"order_payment"} 
[2025-07-29 16:10:37] local.INFO: Processing successful order payment status change {"payment_id":"**************715174","entity_id":"4"} 
[2025-07-29 16:11:07] local.INFO: MyFatoorah unified payment webhook received {"paymentId":"**************715174","Id":"**************715174"} 
[2025-07-29 16:11:09] local.INFO: Payment details fetched successfully {"payment_id":"**************715174","response":{"InvoiceId":5981526,"InvoiceStatus":"Paid","InvoiceReference":"**********","CustomerReference":"4","CreatedDate":"2025-07-29T16:04:18.52","ExpiryDate":"August 1, 2025","ExpiryTime":"16:04:18.520","InvoiceValue":30.5,"Comments":null,"CustomerName":"dqwee31232","CustomerMobile":"+************","CustomerEmail":"<EMAIL>","UserDefinedField":"order_payment","InvoiceDisplayValue":"30.500 SR","DueDeposit":29.448,"DepositStatus":"Not Deposited","InvoiceItems":[{"ItemName":"شامبو شعر احترافي","Quantity":3,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"شامبو شعر احترافي","Quantity":1,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Delivery Fee","Quantity":1,"UnitPrice":15,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Total Adjustment","Quantity":1,"UnitPrice":-46.5,"Weight":null,"Width":null,"Height":null,"Depth":null}],"InvoiceTransactions":[{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}],"Suppliers":[],"InvoiceError":"","focusTransaction":{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}}} 
[2025-07-29 16:11:09] local.INFO: Payment details fetched from MyFatoorah {"payment_id":"**************715174","status":"Paid","entity_id":"4","payment_type":"order_payment"} 
[2025-07-29 16:11:09] local.INFO: Processing successful order payment status change {"payment_id":"**************715174","entity_id":"4"} 
[2025-07-29 16:11:24] local.INFO: MyFatoorah unified payment webhook received {"paymentId":"**************715174","Id":"**************715174"} 
[2025-07-29 16:11:25] local.INFO: Payment details fetched successfully {"payment_id":"**************715174","response":{"InvoiceId":5981526,"InvoiceStatus":"Paid","InvoiceReference":"**********","CustomerReference":"4","CreatedDate":"2025-07-29T16:04:18.52","ExpiryDate":"August 1, 2025","ExpiryTime":"16:04:18.520","InvoiceValue":30.5,"Comments":null,"CustomerName":"dqwee31232","CustomerMobile":"+************","CustomerEmail":"<EMAIL>","UserDefinedField":"order_payment","InvoiceDisplayValue":"30.500 SR","DueDeposit":29.448,"DepositStatus":"Not Deposited","InvoiceItems":[{"ItemName":"شامبو شعر احترافي","Quantity":3,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"شامبو شعر احترافي","Quantity":1,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Delivery Fee","Quantity":1,"UnitPrice":15,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Total Adjustment","Quantity":1,"UnitPrice":-46.5,"Weight":null,"Width":null,"Height":null,"Depth":null}],"InvoiceTransactions":[{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}],"Suppliers":[],"InvoiceError":"","focusTransaction":{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}}} 
[2025-07-29 16:11:25] local.INFO: Payment details fetched from MyFatoorah {"payment_id":"**************715174","status":"Paid","entity_id":"4","payment_type":"order_payment"} 
[2025-07-29 16:11:25] local.INFO: Processing successful order payment status change {"payment_id":"**************715174","entity_id":"4"} 
[2025-07-29 16:11:25] local.ERROR: MyFatoorah order payment verification failed {"payment_id":"**************715174","order_id":"4","error":"Attempt to read property \"providerSubOrders\" on null"} 
[2025-07-29 16:11:25] local.WARNING: order payment webhook verification failed {"payment_id":"**************715174","entity_id":"4","type":"order","error":"Payment verification failed: Attempt to read property \"providerSubOrders\" on null"} 
[2025-07-29 16:14:03] local.INFO: MyFatoorah unified payment webhook received {"paymentId":"**************715174","Id":"**************715174"} 
[2025-07-29 16:14:04] local.INFO: Payment details fetched successfully {"payment_id":"**************715174","response":{"InvoiceId":5981526,"InvoiceStatus":"Paid","InvoiceReference":"**********","CustomerReference":"4","CreatedDate":"2025-07-29T16:04:18.52","ExpiryDate":"August 1, 2025","ExpiryTime":"16:04:18.520","InvoiceValue":30.5,"Comments":null,"CustomerName":"dqwee31232","CustomerMobile":"+************","CustomerEmail":"<EMAIL>","UserDefinedField":"order_payment","InvoiceDisplayValue":"30.500 SR","DueDeposit":29.448,"DepositStatus":"Not Deposited","InvoiceItems":[{"ItemName":"شامبو شعر احترافي","Quantity":3,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"شامبو شعر احترافي","Quantity":1,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Delivery Fee","Quantity":1,"UnitPrice":15,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Total Adjustment","Quantity":1,"UnitPrice":-46.5,"Weight":null,"Width":null,"Height":null,"Depth":null}],"InvoiceTransactions":[{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}],"Suppliers":[],"InvoiceError":"","focusTransaction":{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}}} 
[2025-07-29 16:14:04] local.INFO: Payment details fetched from MyFatoorah {"payment_id":"**************715174","status":"Paid","entity_id":"4","payment_type":"order_payment"} 
[2025-07-29 16:14:04] local.INFO: Processing successful order payment status change {"payment_id":"**************715174","entity_id":"4"} 
[2025-07-29 16:14:05] local.INFO: Order found for MyFatoorah callback {"order_id":4,"order_number":"7309825","is_trashed":true,"payment_status":"pending"} 
[2025-07-29 16:14:05] local.ERROR: MyFatoorah order payment verification failed {"payment_id":"**************715174","order_id":"4","error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'field list' (Connection: mysql, SQL: update `orders` set `payment_status` = paid, `payment_reference` = **************715174, `status` = processing, `orders`.`updated_at` = 2025-07-29 16:14:05 where `id` = 4)"} 
[2025-07-29 16:14:05] local.WARNING: order payment webhook verification failed {"payment_id":"**************715174","entity_id":"4","type":"order","error":"Payment verification failed: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'field list' (Connection: mysql, SQL: update `orders` set `payment_status` = paid, `payment_reference` = **************715174, `status` = processing, `orders`.`updated_at` = 2025-07-29 16:14:05 where `id` = 4)"} 
[2025-07-29 16:15:37] local.INFO: MyFatoorah unified payment webhook received {"paymentId":"**************715174","Id":"**************715174"} 
[2025-07-29 16:15:39] local.INFO: Payment details fetched successfully {"payment_id":"**************715174","response":{"InvoiceId":5981526,"InvoiceStatus":"Paid","InvoiceReference":"**********","CustomerReference":"4","CreatedDate":"2025-07-29T16:04:18.52","ExpiryDate":"August 1, 2025","ExpiryTime":"16:04:18.520","InvoiceValue":30.5,"Comments":null,"CustomerName":"dqwee31232","CustomerMobile":"+************","CustomerEmail":"<EMAIL>","UserDefinedField":"order_payment","InvoiceDisplayValue":"30.500 SR","DueDeposit":29.448,"DepositStatus":"Not Deposited","InvoiceItems":[{"ItemName":"شامبو شعر احترافي","Quantity":3,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"شامبو شعر احترافي","Quantity":1,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Delivery Fee","Quantity":1,"UnitPrice":15,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Total Adjustment","Quantity":1,"UnitPrice":-46.5,"Weight":null,"Width":null,"Height":null,"Depth":null}],"InvoiceTransactions":[{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}],"Suppliers":[],"InvoiceError":"","focusTransaction":{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}}} 
[2025-07-29 16:15:39] local.INFO: Payment details fetched from MyFatoorah {"payment_id":"**************715174","status":"Paid","entity_id":"4","payment_type":"order_payment"} 
[2025-07-29 16:15:39] local.INFO: Processing successful order payment status change {"payment_id":"**************715174","entity_id":"4"} 
[2025-07-29 16:15:40] local.INFO: Order found for MyFatoorah callback {"order_id":4,"order_number":"7309825","is_trashed":true,"payment_status":"pending"} 
[2025-07-29 16:15:40] local.ERROR: MyFatoorah order payment verification failed {"payment_id":"**************715174","order_id":"4","error":"SQLSTATE[01000]: Warning: 1265 Data truncated for column 'payment_status' at row 1 (Connection: mysql, SQL: update `orders` set `current_status` = processing, `payment_status` = paid, `payment_reference` = **************715174, `orders`.`updated_at` = 2025-07-29 16:15:40 where `id` = 4)"} 
[2025-07-29 16:15:40] local.WARNING: order payment webhook verification failed {"payment_id":"**************715174","entity_id":"4","type":"order","error":"Payment verification failed: SQLSTATE[01000]: Warning: 1265 Data truncated for column 'payment_status' at row 1 (Connection: mysql, SQL: update `orders` set `current_status` = processing, `payment_status` = paid, `payment_reference` = **************715174, `orders`.`updated_at` = 2025-07-29 16:15:40 where `id` = 4)"} 
[2025-07-29 16:16:05] local.INFO: MyFatoorah unified payment webhook received {"paymentId":"**************715174","Id":"**************715174"} 
[2025-07-29 16:16:07] local.INFO: Payment details fetched successfully {"payment_id":"**************715174","response":{"InvoiceId":5981526,"InvoiceStatus":"Paid","InvoiceReference":"**********","CustomerReference":"4","CreatedDate":"2025-07-29T16:04:18.52","ExpiryDate":"August 1, 2025","ExpiryTime":"16:04:18.520","InvoiceValue":30.5,"Comments":null,"CustomerName":"dqwee31232","CustomerMobile":"+************","CustomerEmail":"<EMAIL>","UserDefinedField":"order_payment","InvoiceDisplayValue":"30.500 SR","DueDeposit":29.448,"DepositStatus":"Not Deposited","InvoiceItems":[{"ItemName":"شامبو شعر احترافي","Quantity":3,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"شامبو شعر احترافي","Quantity":1,"UnitPrice":15.5,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Delivery Fee","Quantity":1,"UnitPrice":15,"Weight":null,"Width":null,"Height":null,"Depth":null},{"ItemName":"Total Adjustment","Quantity":1,"UnitPrice":-46.5,"Weight":null,"Width":null,"Height":null,"Depth":null}],"InvoiceTransactions":[{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}],"Suppliers":[],"InvoiceError":"","focusTransaction":{"TransactionDate":"2025-07-29T16:06:02.5366667","PaymentGateway":"VISA/MASTER","ReferenceId":"************","TrackId":"29-07-2025_2897151","TransactionId":"290734","PaymentId":"**************715174","AuthorizationId":"950219","TransactionStatus":"Succss","TransationValue":"30.500","CustomerServiceCharge":"0.000","TotalServiceCharge":"0.915","DueValue":"30.500","PaidCurrency":"SR","PaidCurrencyValue":"30.500","VatAmount":"0.137","IpAddress":"**************","Country":"Egypt","Currency":"SR","Error":null,"CardNumber":"450875xxxxxx1019","ErrorCode":"","ECI":"05","Card":{"NameOnCard":"mahmoud hamed","Number":"450875xxxxxx1019","PanHash":"b4eda25d8a64929c02240406fc5cdc5417e4d021e1d055e43edb61e8d4903cfa","ExpiryMonth":"02","ExpiryYear":"29","Brand":"Visa","Issuer":"The Co-Operative Bank Plc","IssuerCountry":"GBR","FundingMethod":"debit"}}}} 
[2025-07-29 16:16:07] local.INFO: Payment details fetched from MyFatoorah {"payment_id":"**************715174","status":"Paid","entity_id":"4","payment_type":"order_payment"} 
[2025-07-29 16:16:07] local.INFO: Processing successful order payment status change {"payment_id":"**************715174","entity_id":"4"} 
[2025-07-29 16:16:09] local.INFO: Order found for MyFatoorah callback {"order_id":4,"order_number":"7309825","is_trashed":true,"payment_status":"pending"} 
[2025-07-29 16:16:09] local.INFO: Cart cleared after payment confirmation {"order_id":4,"order_number":"7309825","user_id":28} 
[2025-07-29 16:16:09] local.INFO: Order payment successful {"order_id":4,"order_number":"7309825","payment_id":"**************715174","amount":"30.50","cart_cleared":true} 
[2025-07-29 16:20:24] local.ERROR: Command "check-notifications" is not defined.

Did you mean one of these?
    notifications:table
    orders:check-notifications {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"check-notifications\" is not defined.

Did you mean one of these?
    notifications:table
    orders:check-notifications at D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\symfony\\console\\Application.php:725)
[stacktrace]
#0 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find('check-notificat...')
#1 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\Workstation\\Taswk\\sorriso-backend\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 {main}
"} 
[2025-07-29 16:22:51] local.ERROR: Command "add_image_to_payment_methods_table" is not defined. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"add_image_to_payment_methods_table\" is not defined. at D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\symfony\\console\\Application.php:725)
[stacktrace]
#0 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find('add_image_to_pa...')
#1 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\Workstation\\Taswk\\sorriso-backend\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 {main}
"} 
[2025-07-29 16:29:58] local.ERROR: Undefined constant App\Models\PaymentMethod::IMAGEPATH {"exception":"[object] (Error(code: 0): Undefined constant App\\Models\\PaymentMethod::IMAGEPATH at D:\\Workstation\\Taswk\\sorriso-backend\\app\\Models\\BaseModel.php:53)
[stacktrace]
#0 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(1122): App\\Models\\BaseModel->setImageAttribute('storage/images/...')
#1 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(1028): Illuminate\\Database\\Eloquent\\Model->setMutatedAttributeValue('image', 'storage/images/...')
#2 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\spatie\\laravel-translatable\\src\\HasTranslations.php(61): Illuminate\\Database\\Eloquent\\Model->setAttribute('image', 'storage/images/...')
#3 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(539): App\\Models\\PaymentMethod->setAttribute('image', 'storage/images/...')
#4 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(633): Illuminate\\Database\\Eloquent\\Model->fill(Array)
#5 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1655): Illuminate\\Database\\Eloquent\\Model->newInstance(Array)
#6 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1127): Illuminate\\Database\\Eloquent\\Builder->newModelInstance(Array)
#7 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#8 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2372): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#9 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2384): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#10 D:\\Workstation\\Taswk\\sorriso-backend\\database\\seeders\\PaymentMethodSeeder.php(68): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#11 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\PaymentMethodSeeder->run()
#12 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#15 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#16 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#17 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#18 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Seeder->__invoke()
#19 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#20 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(69): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#21 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#22 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#25 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#26 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#27 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\Workstation\\Taswk\\sorriso-backend\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 {main}
"} 
[2025-07-29 16:30:25] local.ERROR: Trait "App\Models\uploadTrait" not found {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Trait \"App\\Models\\uploadTrait\" not found at D:\\Workstation\\Taswk\\sorriso-backend\\app\\Models\\PaymentMethod.php:10)
[stacktrace]
#0 {main}
"} 
[2025-07-29 16:31:04] local.ERROR: syntax error, unexpected token "=", expecting variable {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"=\", expecting variable at D:\\Workstation\\Taswk\\sorriso-backend\\app\\Models\\PaymentMethod.php:15)
[stacktrace]
#0 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('D:\\\\Workstation\\\\...')
#1 D:\\Workstation\\Taswk\\sorriso-backend\\database\\seeders\\PaymentMethodSeeder.php(68): Composer\\Autoload\\ClassLoader->loadClass('App\\\\Models\\\\Paym...')
#2 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\PaymentMethodSeeder->run()
#3 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#4 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#5 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#6 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#7 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#8 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#9 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Seeder->__invoke()
#10 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#11 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(69): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#12 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#13 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#17 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#18 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#19 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#20 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 D:\\Workstation\\Taswk\\sorriso-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 D:\\Workstation\\Taswk\\sorriso-backend\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 {main}
"} 
[2025-07-29 16:31:06] local.ERROR: syntax error, unexpected token "=", expecting variable {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected token \"=\", expecting variable at D:\\Workstation\\Taswk\\sorriso-backend\\app\\Models\\PaymentMethod.php:15)
[stacktrace]
#0 {main}
"} 
[2025-07-29 16:31:24] local.ERROR: syntax error, unexpected token "=", expecting variable {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): syntax error, unexpected token \"=\", expecting variable at D:\\Workstation\\Taswk\\sorriso-backend\\app\\Models\\PaymentMethod.php:15)
[stacktrace]
#0 {main}
"} 
[2025-07-29 16:31:54] local.INFO: setImageAttribute called {"value_type":"string","is_object":false,"class":"not an object","model":"App\\Models\\PaymentMethod","image_path":"paymentmethods"} 
[2025-07-29 16:31:54] local.INFO: uploadAllTyps called {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-29 16:31:54] local.INFO: Uploading image {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-29 16:31:56] local.INFO: Image uploaded successfully {"new_image":"1753795914_7619.png"} 
[2025-07-29 16:31:56] local.INFO: setImageAttribute called {"value_type":"string","is_object":false,"class":"not an object","model":"App\\Models\\PaymentMethod","image_path":"paymentmethods"} 
[2025-07-29 16:31:56] local.INFO: uploadAllTyps called {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-29 16:31:56] local.INFO: Uploading image {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-29 16:31:56] local.INFO: Image uploaded successfully {"new_image":"1753795916_8034.png"} 
[2025-07-29 16:31:56] local.INFO: setImageAttribute called {"value_type":"string","is_object":false,"class":"not an object","model":"App\\Models\\PaymentMethod","image_path":"paymentmethods"} 
[2025-07-29 16:31:56] local.INFO: uploadAllTyps called {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-29 16:31:56] local.INFO: Uploading image {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-29 16:31:56] local.INFO: Image uploaded successfully {"new_image":"1753795916_6276.png"} 
[2025-07-29 16:31:56] local.INFO: setImageAttribute called {"value_type":"string","is_object":false,"class":"not an object","model":"App\\Models\\PaymentMethod","image_path":"paymentmethods"} 
[2025-07-29 16:31:56] local.INFO: uploadAllTyps called {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-29 16:31:56] local.INFO: Uploading image {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-29 16:31:56] local.INFO: Image uploaded successfully {"new_image":"1753795916_3102.png"} 
[2025-07-29 16:33:47] local.INFO: setImageAttribute called {"value_type":"string","is_object":false,"class":"not an object","model":"App\\Models\\PaymentMethod","image_path":"paymentmethods"} 
[2025-07-29 16:33:47] local.INFO: uploadAllTyps called {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-29 16:33:47] local.INFO: Uploading image {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-29 16:33:47] local.INFO: Image uploaded successfully {"new_image":"1753796027_6107.png"} 
[2025-07-29 16:33:47] local.INFO: setImageAttribute called {"value_type":"string","is_object":false,"class":"not an object","model":"App\\Models\\PaymentMethod","image_path":"paymentmethods"} 
[2025-07-29 16:33:47] local.INFO: uploadAllTyps called {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-29 16:33:47] local.INFO: Uploading image {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-29 16:33:47] local.INFO: Image uploaded successfully {"new_image":"1753796027_6814.png"} 
[2025-07-29 16:33:47] local.INFO: setImageAttribute called {"value_type":"string","is_object":false,"class":"not an object","model":"App\\Models\\PaymentMethod","image_path":"paymentmethods"} 
[2025-07-29 16:33:47] local.INFO: uploadAllTyps called {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-29 16:33:47] local.INFO: Uploading image {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-29 16:33:47] local.INFO: Image uploaded successfully {"new_image":"1753796027_7477.png"} 
[2025-07-29 16:33:47] local.INFO: setImageAttribute called {"value_type":"string","is_object":false,"class":"not an object","model":"App\\Models\\PaymentMethod","image_path":"paymentmethods"} 
[2025-07-29 16:33:47] local.INFO: uploadAllTyps called {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-29 16:33:47] local.INFO: Uploading image {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-29 16:33:47] local.INFO: Image uploaded successfully {"new_image":"1753796027_3998.png"} 
[2025-07-30 13:50:42] local.INFO: setImageAttribute called {"value_type":"string","is_object":false,"class":"not an object","model":"App\\Models\\PaymentMethod","image_path":"paymentmethods"} 
[2025-07-30 13:50:42] local.INFO: uploadAllTyps called {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-30 13:50:42] local.INFO: Uploading image {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-30 13:50:42] local.INFO: Image uploaded successfully {"new_image":"1753872642_5318.png"} 
[2025-07-30 13:50:42] local.INFO: setImageAttribute called {"value_type":"string","is_object":false,"class":"not an object","model":"App\\Models\\PaymentMethod","image_path":"paymentmethods"} 
[2025-07-30 13:50:42] local.INFO: uploadAllTyps called {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-30 13:50:42] local.INFO: Uploading image {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-30 13:50:42] local.INFO: Image uploaded successfully {"new_image":"1753872642_4335.png"} 
[2025-07-30 13:50:42] local.INFO: setImageAttribute called {"value_type":"string","is_object":false,"class":"not an object","model":"App\\Models\\PaymentMethod","image_path":"paymentmethods"} 
[2025-07-30 13:50:42] local.INFO: uploadAllTyps called {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-30 13:50:42] local.INFO: Uploading image {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-30 13:50:42] local.INFO: Image uploaded successfully {"new_image":"1753872642_9446.png"} 
[2025-07-30 13:50:42] local.INFO: setImageAttribute called {"value_type":"string","is_object":false,"class":"not an object","model":"App\\Models\\PaymentMethod","image_path":"paymentmethods"} 
[2025-07-30 13:50:42] local.INFO: uploadAllTyps called {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-30 13:50:42] local.INFO: Uploading image {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-30 13:50:42] local.INFO: Image uploaded successfully {"new_image":"1753872642_4028.png"} 
[2025-07-30 13:50:42] local.INFO: setImageAttribute called {"value_type":"string","is_object":false,"class":"not an object","model":"App\\Models\\PaymentMethod","image_path":"paymentmethods"} 
[2025-07-30 13:50:42] local.INFO: uploadAllTyps called {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-30 13:50:42] local.INFO: Uploading image {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-30 13:50:42] local.INFO: Image uploaded successfully {"new_image":"1753872642_6917.png"} 
[2025-07-30 13:50:42] local.INFO: setImageAttribute called {"value_type":"string","is_object":false,"class":"not an object","model":"App\\Models\\PaymentMethod","image_path":"paymentmethods"} 
[2025-07-30 13:50:42] local.INFO: uploadAllTyps called {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-30 13:50:42] local.INFO: Uploading image {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-30 13:50:42] local.INFO: Image uploaded successfully {"new_image":"1753872642_7675.png"} 
[2025-07-30 13:57:14] local.INFO: setImageAttribute called {"value_type":"string","is_object":false,"class":"not an object","model":"App\\Models\\PaymentMethod","image_path":"paymentmethods"} 
[2025-07-30 13:57:14] local.INFO: uploadAllTyps called {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-30 13:57:14] local.INFO: Uploading image {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-30 13:57:16] local.ERROR: Failed to decode base64 image: Unable to decode input  
[2025-07-30 13:57:16] local.INFO: Image uploaded successfully {"new_image":"default.png"} 
[2025-07-30 13:57:16] local.INFO: setImageAttribute called {"value_type":"string","is_object":false,"class":"not an object","model":"App\\Models\\PaymentMethod","image_path":"paymentmethods"} 
[2025-07-30 13:57:16] local.INFO: uploadAllTyps called {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-30 13:57:16] local.INFO: Uploading image {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-30 13:57:16] local.ERROR: Failed to decode base64 image: Unable to decode input  
[2025-07-30 13:57:16] local.INFO: Image uploaded successfully {"new_image":"default.png"} 
[2025-07-30 13:57:16] local.INFO: setImageAttribute called {"value_type":"string","is_object":false,"class":"not an object","model":"App\\Models\\PaymentMethod","image_path":"paymentmethods"} 
[2025-07-30 13:57:16] local.INFO: uploadAllTyps called {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-30 13:57:16] local.INFO: Uploading image {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-30 13:57:16] local.ERROR: Failed to decode base64 image: Unable to decode input  
[2025-07-30 13:57:16] local.INFO: Image uploaded successfully {"new_image":"default.png"} 
[2025-07-30 13:57:16] local.INFO: setImageAttribute called {"value_type":"string","is_object":false,"class":"not an object","model":"App\\Models\\PaymentMethod","image_path":"paymentmethods"} 
[2025-07-30 13:57:16] local.INFO: uploadAllTyps called {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-30 13:57:16] local.INFO: Uploading image {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-30 13:57:16] local.ERROR: Failed to decode base64 image: Unable to decode input  
[2025-07-30 13:57:16] local.INFO: Image uploaded successfully {"new_image":"default.png"} 
[2025-07-30 13:57:16] local.INFO: setImageAttribute called {"value_type":"string","is_object":false,"class":"not an object","model":"App\\Models\\PaymentMethod","image_path":"paymentmethods"} 
[2025-07-30 13:57:16] local.INFO: uploadAllTyps called {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-30 13:57:16] local.INFO: Uploading image {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-30 13:57:16] local.ERROR: Failed to decode base64 image: Unable to decode input  
[2025-07-30 13:57:16] local.INFO: Image uploaded successfully {"new_image":"default.png"} 
[2025-07-30 13:57:16] local.INFO: setImageAttribute called {"value_type":"string","is_object":false,"class":"not an object","model":"App\\Models\\PaymentMethod","image_path":"paymentmethods"} 
[2025-07-30 13:57:16] local.INFO: uploadAllTyps called {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-30 13:57:16] local.INFO: Uploading image {"file_type":"string","is_object":false,"class":"not an object","directory":"paymentmethods"} 
[2025-07-30 13:57:16] local.ERROR: Failed to decode base64 image: Unable to decode input  
[2025-07-30 13:57:16] local.INFO: Image uploaded successfully {"new_image":"default.png"} 
