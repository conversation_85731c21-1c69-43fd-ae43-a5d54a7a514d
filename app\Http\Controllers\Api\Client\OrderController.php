<?php
namespace App\Http\Controllers\Api\Client;

use App\Http\Resources\Api\Client\LoyalityPointResource;
use App\Models\Order;
use App\Services\Responder;
use App\Traits\ResponseTrait;
use App\Services\OrderService;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use App\Http\Resources\Api\Order\OrderResource;
use App\Http\Requests\Api\Order\CancelOrderRequest;
use App\Http\Requests\Api\Order\CreateOrderRequest;
use App\Http\Resources\Api\Client\OrderDetailsResource;
use App\Http\Resources\Api\Client\ClientPaymentResource;
class OrderController extends Controller
{
    use ResponseTrait;

    /**
     * @var OrderService
     */
    protected $orderService;

    /**
     * OrderController constructor.
     *
     * @param OrderService $orderService
     */
    public function __construct(OrderService $orderService)
    {
        $this->orderService = $orderService;
    }

    /**
     * Get user orders
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getOrders()
    {
        $user   = auth()->user();
        $filters = ['status' => request('status') , 'sort' => request('sort'), // Add sort filter
    ];
        $orders = $this->orderService->getUserOrders($user , $filters);

        return Responder::success(OrderResource::collection($orders));
    }

    /**
     * Get order details
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getOrder($id)
    {
        $user  = auth()->user();
        $order = $this->orderService->getUserOrder($user, $id);

        return Responder::success( new OrderDetailsResource($order));
    }

    /**
     * Create a new order from the cart
     *
     * @param \App\Http\Requests\Api\Order\CreateOrderRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createOrder(CreateOrderRequest $request)
    {
        try {
            $user = auth()->user();
            $result = $this->orderService->createOrderFromCart($user, $request->validated());
            // If the result is an array and has a status key, use the new structure
            if (is_array($result) && isset($result['status'])) {
                if ($result['status'] === 200) {
                    return response()->json([
                        'status' => 200,
                        'message' => $result['message'],
                        'data' => $result['data']
                    ], 200);
                } else {
                    return response()->json([
                        'status' => $result['status'],
                        'message' => $result['message'],
                        'data' => $result['data'] ?? null
                    ], $result['status']);
                }
            }

            // Normal order creation success (wallet, bank transfer)
            $orderResource = new \App\Http\Resources\Api\Order\OrderResource($result);
            return response()->json([
                'status' => 200,
                'message' => __('apis.order_created'),
                'data' => $orderResource->toArray(request())
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 422,
                'message' => $e->getMessage(),
                'data' => null
            ], 422);
        }
    }

    /**
     * Cancel an order
     *
     * @param \App\Http\Requests\Api\Order\CancelOrderRequest $request
     * @return \Illuminate\Http\JsonResponse
     */

     public function SendCancelRequest(CancelOrderRequest $request)
     {
        try {
            $user = auth()->user();
            $order = Order::where('id', $request->order_id)
                          ->where('user_id', $user->id)
                          ->firstOrFail();

            // Check if order can be cancelled
            if (!in_array($order->current_status, ['pending_payment', 'processing', 'confirmed'])) {
                return Responder::error(__('apis.order_cannot_be_cancelled_at_this_stage'), [], 400);
            }

            // Check if order is already requested for cancellation
            if ($order->current_status === 'request_cancel') {
                return Responder::error(__('apis.cancellation_request_already_submitted'), [], 400);
            }

            // Update order status to request_cancel
            $order->update([
                'current_status' => 'request_cancel',
                'cancel_reason_id' => $request->cancel_reason_id,
            ]);

            // Send notification to all admins about cancellation request
            $this->sendOrderCancellationNotificationToAdmins($order->id);

            return Responder::success(null, ['message' => __('apis.cancellation_request_submitted_successfully')]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return Responder::error(__('apis.order_not_found'), [], 404);
        } catch (\Exception $e) {
            return Responder::error(__('apis.failed_to_submit_cancellation_request'), [], 500);
        }
     }
    public function cancelOrder(CancelOrderRequest $request)
    {
        try {
            $user      = auth()->user();
            $validated = $request->validated();
            $order = $user->orders()->findOrFail($validated['order_id']);

            $cancelledOrder = $this->orderService->cancelOrder($order, $validated['reason'] ?? null, auth()->user());

            return Responder::success(new OrderResource($cancelledOrder), ['message' => __('apis.order_canceled')]);
        } catch (\Exception $e) {
            return Responder::error($e->getMessage(), [], 422);
        }
    }

    /**
     * Confirm payment (webhook endpoint)
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function confirmPayment(\Illuminate\Http\Request $request)
    {
        try {
            $orderNumber = $request->input('order_number');
            $paymentReference = $request->input('payment_reference');
            $status = $request->input('status'); // success, failed

            $order = \App\Models\Order::where('order_number', $orderNumber)->firstOrFail();

            if ($status === 'success') {
                $confirmedOrder = $this->orderService->confirmPayment($order, $paymentReference);
                return Responder::success(new OrderResource($confirmedOrder), ['message' => 'Payment confirmed successfully']);
            } else {
                // Payment failed - restore product quantities
                $this->orderService->cancelOrder($order, 'Payment failed');
                return Responder::error('Payment failed', [], 422);
            }

        } catch (\Exception $e) {
            return Responder::error($e->getMessage(), [], 422);
        }
    }

    /**
     * Get order cost breakdown
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getOrderCostBreakdown()
    {
        try {
            $user = auth()->user();
            $cart = $user->cart;

            if (!$cart || $cart->items()->count() === 0) {
                return Responder::error('Cart is empty', [], 422);
            }

            $breakdown = [
                'services_total' => $cart->hasServices() ? $cart->items()->whereHas('item', function($q) {
                    $q->where('item_type', 'App\Models\Service');
                })->sum('total') : 0,
                'products_total' => $cart->hasProducts() ? $cart->items()->whereHas('item', function($q) {
                    $q->where('item_type', 'App\Models\Product');
                })->sum('total') : 0,
                'subtotal' => $cart->subtotal,
                'discount_amount' => $cart->discount_amount,
                'coupon_code' => $cart->coupon_code,
                'booking_fee' => $cart->booking_fee,
                'home_service_fee' => $cart->home_service_fee ?? 0,
                'delivery_fee' => $cart->delivery_fee ?? 0,
                'loyalty_points_used' => $cart->loyalty_points_used,
                'loyalty_points_value' => $cart->loyalty_points_used * 0.1, // Assuming 1 point = 0.1 SAR
                'total' => $cart->total,
                'final_total' => $cart->total - ($cart->loyalty_points_used * 0.1),
            ];

            return Responder::success($breakdown, ['message' => 'Cost breakdown retrieved successfully']);

        } catch (\Exception $e) {
            return Responder::error($e->getMessage(), [], 500);
        }
    }

    /**
     * Get bank transfer details
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBankTransferDetails()
    {
        try {
            $bankDetails = [
                'bank_name' => 'البنك الأهلي السعودي',
                'beneficiary_name' => 'شركة سوريسو للتقنية',
                'account_number' => '**********',
                'iban' => '************************',
                'instructions' => 'Please include your order number in the transfer reference',
            ];

            return Responder::success($bankDetails, ['message' => 'Bank transfer details retrieved successfully']);

        } catch (\Exception $e) {
            return Responder::error($e->getMessage(), [], 500);
        }
    }

    /**
     * Get available time slots for booking
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAvailableTimeSlots(\Illuminate\Http\Request $request)
    {
        try {
            $date = $request->input('date', now()->format('Y-m-d'));
            $providerId = $request->input('provider_id');

            // Generate time slots (this would be more complex in real implementation)
            $timeSlots = [];
            $startTime = 9; // 9 AM
            $endTime = 18; // 6 PM

            for ($hour = $startTime; $hour < $endTime; $hour++) {
                $timeSlots[] = [
                    'time' => sprintf('%02d:00', $hour),
                    'display_time' => sprintf('%02d:00', $hour),
                    'available' => true, // This would check actual availability
                ];
                $timeSlots[] = [
                    'time' => sprintf('%02d:30', $hour),
                    'display_time' => sprintf('%02d:30', $hour),
                    'available' => true,
                ];
            }

            return Responder::success($timeSlots, ['message' => 'Available time slots retrieved successfully']);

        } catch (\Exception $e) {
            return Responder::error($e->getMessage(), [], 500);
        }
    }

    /**
     * Get provider working hours
     *
     * @param int $providerId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProviderWorkingHours($providerId)
    {
        try {
            $provider = \App\Models\Provider::findOrFail($providerId);

            // Get working hours for the next 7 days
            $workingHours = [];
            for ($i = 0; $i < 7; $i++) {
                $date = now()->addDays($i);
                $dayName = strtolower($date->format('l'));

                // This would get actual working hours from provider settings
                $workingHours[] = [
                    'date' => $date->format('Y-m-d'),
                    'day_name' => $date->format('l'),
                    'is_available' => true, // Check if provider works on this day
                    'start_time' => '09:00',
                    'end_time' => '18:00',
                ];
            }

            return Responder::success($workingHours, ['message' => 'Provider working hours retrieved successfully']);

        } catch (\Exception $e) {
            return Responder::error('Provider not found', [], 404);
        }
    }

    /**
     * Get available payment gateways for cart
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function paymentGateways()
    {
        try {
            $user = auth()->user();
            $cart = $user->cart;

            if (!$cart || $cart->items()->count() === 0) {
                return Responder::error('Cart is empty', [], 422);
            }

            $myfatoorahService = app(\App\Services\Myfatoorah\OrderPaymentService::class);
            $result = $myfatoorahService->getAvailableGateways($cart->total);

            if ($result['success']) {
                return Responder::success([
                    'cart_total' => $cart->total,
                    'gateways' => $result['gateways']
                ]);
            } else {
                return Responder::error($result['message']);
            }

        } catch (\Exception $e) {
            return Responder::error($e->getMessage(), [], 422);
        }
    }

    public function payments()
    {
       $paymnets =  $this->orderService->ClientOnlinePayments()->get();
        return Responder::success(ClientPaymentResource::collection($paymnets));

    }

    public function loyalityPoints()
    {
        if(request()->has('earn')){
            $paymnets =  $this->orderService->ClientPayments()->where('loyalty_points_earned' , '>' , 0)->get();

        }else{
            $paymnets =  $this->orderService->ClientPayments()->where('loyalty_points_used' , '>' , 0)->get();

        }
        // Get loyalty points settings
        $appInfo = \App\Models\SiteSetting::pluck('value', 'key')->toArray();
        $settings = \App\Services\SettingService::appInformations($appInfo);

        // Calculate total points and their SAR value
        $totalPoints = $this->orderService->ClientPayments()->sum('loyalty_points_earned');
        $pointValue = $settings['loyalty_points_redeem_rate'] ?? 0.1; // Default 0.1 SAR per point
        $pointsValueInSAR = $totalPoints * $pointValue;

        return Responder::success([
            'points' => $totalPoints,
            'points_value_sar' => round($pointsValueInSAR, 2),
            'list' => LoyalityPointResource::collection($paymnets)
        ]);

    }

    public function downloadInvoice($orderId)
    {
        $user = auth()->user();

        if (!$user || $user->type !== 'client') {
            return Responder::error(__('apis.unauthorized'), [], 401);
        }

        // Get the order with all relationships
        $order = Order::with(['user', 'provider', 'address', 'paymentMethod', 'items.item', 'providerSubOrders.provider.user'])
            ->where('user_id', $user->id)
            ->findOrFail($orderId);

        // Get all providers from sub-orders
        $providers = $order->providerSubOrders->map(function($subOrder) {
            return [
                'id' => $subOrder->provider->id,
                'commercial_name' => $subOrder->provider->commercial_name ?? $subOrder->provider->user->name,
                'phone' => $subOrder->provider->user->country_code . $subOrder->provider->user->phone,
                'email' => $subOrder->provider->user->email ?? 'غير محدد',
                'sub_order_number' => $subOrder->sub_order_number,
                'status' => $subOrder->status,
                'total' => $subOrder->total,
                'provider_share' => $subOrder->provider_share,
            ];
        });

        // Prepare invoice data
        $invoiceData = [
            'order' => $order,
            'customer' => $order->user,
            'providers' => $providers,
            'items' => $order->items,
            'totals' => [
                'subtotal' => $order->subtotal,
                'services_total' => $order->services_total,
                'products_total' => $order->products_total,
                'booking_fee' => $order->booking_fee,
                'home_service_fee' => $order->home_service_fee,
                'delivery_fee' => $order->delivery_fee,
                'discount_amount' => $order->discount_amount,
                'total' => $order->total,
            ],
            'address' => $order->address,
            'payment_method' => $order->paymentMethod,
        ];

        // Generate PDF
        $pdf = \PDF::loadView('invoices.client_order_invoice', $invoiceData);

        $filename = 'invoice_' . $order->order_number . '_' . now()->format('Ymd_His') . '.pdf';

        // Save to storage (public disk)
        Storage::disk('public')->put('invoices/' . $filename, $pdf->output());

        // Generate the URL
        $downloadUrl = Storage::url('invoices/' . $filename);

        return Responder::success([
            'url' => $downloadUrl,
            'filename' => $filename,
        ], __('apis.success'));
    }

    protected function sendNewOrderNotificationToAdmins($orderNum , $id)
    {
        $message = 'يوجد طلب حجز جديد برقم #' . $orderNum;
        $admins = \App\Models\Admin::all();
        foreach ($admins as $admin) {
            $admin->notify(new \App\Notifications\NotifyAdmin([
                'title' => [
                    'ar' => 'طلب جديد',
                    'en' => 'New Order'
                ],
                'body' => [
                    'ar' => $message,
                    'en' => $message
                ],
                'type' => 'new_order',
                'order_id' => $id   
            ]));
        }
    }

    protected function sendOrderCancellationNotificationToAdmins($orderId)
    {
        $message = 'تم تقديم طلب إلغاء للطلب رقم #' . $orderId;
        $admins = \App\Models\Admin::all();
        foreach ($admins as $admin) {
            $admin->notify(new \App\Notifications\NotifyAdmin([
                'title' => [
                    'ar' => 'طلب إلغاء طلب',
                    'en' => 'Order Cancellation Request'
                ],
                'body' => [
                    'ar' => $message,
                    'en' => $message
                ],
                'type' => 'order_cancel_request',
                'order_id' => $orderId
            ]));
        }
    }

}
