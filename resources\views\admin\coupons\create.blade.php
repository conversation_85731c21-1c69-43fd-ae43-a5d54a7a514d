@extends('admin.layout.master')
{{-- extra css files --}}
@section('css')
    <link rel="stylesheet" type="text/css" href="{{asset('admin/app-assets/css-rtl/plugins/forms/validation/form-validation.css')}}">
    <link rel="stylesheet" type="text/css" href="{{asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css')}}">
    <link rel="stylesheet" type="text/css" href="{{asset('admin/app-assets/vendors/css/pickers/pickadate/pickadate.css')}}">
@endsection
{{-- extra css files --}}

@section('content')
<!-- // Basic multiple Column Form section start -->
<section id="multiple-column-form">
    <div class="row match-height">
        <div class="col-12">
            <div class="card">
                {{-- <div class="card-header">
                    <h4 class="card-title">{{__('admin.add')}}</h4>
                </div> --}}
                <div class="card-content">
                    <div class="card-body">
                        <form  method="POST" action="{{route('admin.coupons.store')}}" class="store form-horizontal" novalidate>
                            @csrf
                            <div class="form-body">
                                <div class="row">

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="coupon-name">{{__('admin.coupon_name')}}</label>
                                            <div class="controls">
                                                <input type="text" name="coupon_name" class="form-control" placeholder="{{__('admin.enter_coupon_name')}}" required data-validation-required-message="{{__('admin.this_field_is_required')}}" >
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="coupon-number">{{__('admin.coupon_number')}}</label>
                                            <div class="controls">
                                                <input type="text" name="coupon_num" class="form-control" placeholder="{{__('admin.enter_coupon_number')}}" required data-validation-required-message="{{__('admin.this_field_is_required')}}" >
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="provider-select">{{__('admin.provider')}}</label>
                                            <div class="controls">
                                                <select name="provider_id" class="select2 form-control" required data-validation-required-message="{{__('admin.this_field_is_required')}}">
                                                    <option value="">{{__('admin.select_provider')}}</option>
                                                    @foreach($providers as $provider)
                                                        <option value="{{$provider->id}}">{{$provider->commercial_name}}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>



                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="first-name-column">{{__('admin.discount_type')}}</label>
                                            <div class="controls">
                                                <select name="type" class="select2 form-control type" required data-validation-required-message="{{__('admin.this_field_is_required')}}" >
                                                    <option value>{{__('admin.select_the_discount_state')}}</option>
                                                    <option value="ratio">{{__('admin.Percentage')}}</option>
                                                    <option value="number">{{__('admin.fixed_number')}}</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="first-name-column">{{__('admin.discount_value')}}</label>
                                            <div class="controls">
                                                <input type="number"  name="discount" class="discount form-control" placeholder="{{__('admin.type_the_value_of_the_discount')}}" required data-validation-required-message="{{__('admin.this_field_is_required')}}" >
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="first-name-column">{{__('admin.larger_value_for_discount')}}</label>
                                            <div class="controls">
                                                <input readonly type="number" name="max_discount" class="max_discount form-control" placeholder="{{__('admin.write_the_greatest_value_for_the_discount')}}" >
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="first-name-column">{{ __('admin.start_date') }}</label>
                                            <div class="controls">
                                                <input type="date" name="start_date" class="form-control" required
                                                    data-validation-required-message="{{ __('admin.this_field_is_required') }}">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="expire-date">{{__('admin.expiry_date')}}</label>
                                            <div class="controls">
                                                <input type="date" name="expire_date" class="form-control">
                                                <small class="form-text text-muted">{{__('admin.leave_empty_for_no_expiry')}}</small>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="max-use">{{__('admin.maximum_usage_count')}}</label>
                                            <div class="controls">
                                                <input type="number" name="max_use" class="form-control" placeholder="{{__('admin.enter_maximum_usage_count')}}" min="0" value="0">
                                                <small class="form-text text-muted">{{__('admin.enter_0_for_unlimited_usage')}}</small>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="minimum-order-value">{{__('admin.minimum_order_value')}}</label>
                                            <div class="controls">
                                                <input type="number" name="minimum_order_value" class="form-control" placeholder="{{__('admin.enter_minimum_order_value')}}" min="0" step="0.01" value="0">
                                                <small class="form-text text-muted">{{__('admin.minimum_order_value_to_use_coupon')}}</small>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <div class="form-group">
                                            <label for="services">{{__('admin.applicable_services')}}</label>
                                            <div class="controls">
                                                <select name="service_ids[]" class="select2 form-control" multiple id="services-select">
                                                    <option value="">{{__('admin.select_services_optional')}}</option>
                                                </select>
                                                <small class="form-text text-muted">{{__('admin.leave_empty_to_apply_to_all_services')}}</small>
                                            </div>
                                        </div>
                                    </div>



                                    <div class="col-12 d-flex justify-content-center mt-3">
                                        <button type="submit" class="btn btn-primary mr-1 mb-1 submit_button">{{__('admin.add')}}</button>
                                        <a href="{{ url()->previous() }}" type="reset" class="btn btn-outline-warning mr-1 mb-1">{{__('admin.back')}}</a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@endsection
@section('js')
    <script src="{{asset('admin/app-assets/vendors/js/forms/validation/jqBootstrapValidation.js')}}"></script>
    <script src="{{asset('admin/app-assets/js/scripts/forms/validation/form-validation.js')}}"></script>
    <script src="{{asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js')}}"></script>
    <script src="{{asset('admin/app-assets/js/scripts/extensions/sweet-alerts.js')}}"></script>
    <script src="{{asset('admin/app-assets/vendors/js/pickers/pickadate/picker.js')}}"></script>
    <script src="{{asset('admin/app-assets/vendors/js/pickers/pickadate/picker.date.js')}}"></script>
    <script src="{{asset('admin/app-assets/js/scripts/pickers/dateTime/pick-a-datetime.js')}}"></script>

    <script>
        $(document).on('change','select[name="provider_id"]', function () {
            var providerId = $(this).val();
            var servicesSelect = $('#services-select');

            // Clear existing options
            servicesSelect.empty().append('<option value="">{{__('admin.select_services_optional')}}</option>');

            if (providerId) {
                // Load services for selected provider
                $.ajax({
                    url: '{{ route("admin.providers.services", ":id") }}'.replace(':id', providerId),
                    type: 'GET',
                    success: function(data) {
                        $.each(data.services, function(index, service) {
                            servicesSelect.append('<option value="' + service.id + '">' + service.name + '</option>');
                        });
                    },
                    error: function() {
                        console.log('Error loading services');
                    }
                });
            }
        });

        $(document).on('change','select[name="type"]', function () {
            if ($(this).val() == 'ratio') {
                $('.max_discount').prop('readonly', false);
            }else{
                $('.max_discount').prop('readonly', true);
            }
        });
    </script>
    <script>
        $(document).on('keyup','.discount', function () {
            if ($('.select2').val() == 'number') {
                $('.max_discount').val($(this).val());
            }else{
                if ($(this).val() > 100) {
                    $(this).val(null)
                    toastr.error('{{__('admin.Percentage_must_not_bigger_than_100')}}')
                }
                $('.max_discount').val(null);
            }
        });

    </script>

    {{-- show selected image script --}}
        @include('admin.shared.addImage')
    {{-- show selected image script --}}

    {{-- submit add form script --}}
        @include('admin.shared.submitAddForm')
    {{-- submit add form script --}}

@endsection