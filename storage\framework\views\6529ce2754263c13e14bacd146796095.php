

<?php $__env->startSection('css'); ?>
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/plugins/forms/validation/form-validation.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/vendors/css/pickers/pickadate/pickadate.css')); ?>">

<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>
<!-- // Basic multiple Column Form section start -->
<section id="multiple-column-form">
    <div class="row match-height">
        <div class="col-12">
            <div class="card">
                
                <div class="card-content">
                    <div class="card-body">
                        <form  method="POST" action="<?php echo e(route('admin.coupons.update' , ['id' => $coupon->id])); ?>" class="store form-horizontal" novalidate>
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PUT'); ?>
                            <div class="form-body">
                                <div class="row">
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="coupon-name"><?php echo e(__('admin.coupon_name')); ?></label>
                                            <div class="controls">
                                                <input type="text" name="coupon_name" value="<?php echo e($coupon->coupon_name ?? ''); ?>" class="form-control" placeholder="<?php echo e(__('admin.enter_coupon_name')); ?>" required data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>" >
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="coupon-number"><?php echo e(__('admin.coupon_number')); ?></label>
                                            <div class="controls">
                                                <input type="text" name="coupon_num" value="<?php echo e($coupon->coupon_num); ?>" class="form-control" placeholder="<?php echo e(__('admin.enter_coupon_number')); ?>" required data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>" >
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="provider-select"><?php echo e(__('admin.provider')); ?></label>
                                            <div class="controls">
                                                <select name="provider_id" class="select2 form-control" required data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>">
                                                    <option value=""><?php echo e(__('admin.select_provider')); ?></option>
                                                    <?php $__currentLoopData = $providers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $provider): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($provider->id); ?>" <?php echo e($coupon->provider_id == $provider->id ? 'selected' : ''); ?>><?php echo e($provider->commercial_name); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="first-name-column"><?php echo e(__('admin.discount_type')); ?></label>
                                            <div class="controls">
                                                <select name="type" class="select2 form-control type" required data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>" >
                                                    <option value><?php echo e(__('admin.select_the_discount_state')); ?></option>
                                                    <option <?php echo e($coupon->type == 'ratio' ? 'selected' : ''); ?> value="ratio"><?php echo e(__('admin.Percentage')); ?></option>
                                                    <option <?php echo e($coupon->type == 'number' ? 'selected' : ''); ?> value="number"><?php echo e(__('admin.fixed_number')); ?></option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="first-name-column"><?php echo e(__('admin.discount_value')); ?></label>
                                            <div class="controls">
                                                <input type="number" value="<?php echo e($coupon->discount); ?>" name="discount" class="discount form-control" placeholder="<?php echo e(__('admin.type_the_value_of_the_discount')); ?>" required data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>" >
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="first-name-column"><?php echo e(__('admin.larger_value_for_discount')); ?></label>
                                            <div class="controls">
                                                <input readonly type="number" name="max_discount" value="<?php echo e($coupon->max_discount); ?>" class="max_discount form-control" placeholder="<?php echo e(__('admin.write_the_greatest_value_for_the_discount')); ?>" >
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="first-name-column"><?php echo e(__('admin.start_date')); ?></label>
                                            <div class="controls">
                                                <input type="date" value="<?php echo e($coupon->start_date ? date('Y-m-d', strtotime($coupon->start_date)) : ''); ?>" name="start_date" class="form-control" required
                                                    data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="expire-date"><?php echo e(__('admin.expiry_date')); ?></label>
                                            <div class="controls">
                                                <input type="date" value="<?php echo e($coupon->expire_date ? date('Y-m-d', strtotime($coupon->expire_date)) : ''); ?>" name="expire_date" class="form-control">
                                                <small class="form-text text-muted"><?php echo e(__('admin.leave_empty_for_no_expiry')); ?></small>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="max-use"><?php echo e(__('admin.maximum_usage_count')); ?></label>
                                            <div class="controls">
                                                <input type="number" name="max_use" class="form-control" placeholder="<?php echo e(__('admin.enter_maximum_usage_count')); ?>" min="0" value="<?php echo e($coupon->max_use ?? 0); ?>">
                                                <small class="form-text text-muted"><?php echo e(__('admin.enter_0_for_unlimited_usage')); ?></small>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="minimum-order-value"><?php echo e(__('admin.minimum_order_value')); ?></label>
                                            <div class="controls">
                                                <input type="number" name="minimum_order_value" class="form-control" placeholder="<?php echo e(__('admin.enter_minimum_order_value')); ?>" min="0" step="0.01" value="<?php echo e($coupon->minimum_order_value ?? 0); ?>">
                                                <small class="form-text text-muted"><?php echo e(__('admin.minimum_order_value_to_use_coupon')); ?></small>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <div class="form-group">
                                            <label for="services"><?php echo e(__('admin.applicable_services')); ?></label>
                                            <div class="controls">
                                                <select name="service_ids[]" class="select2 form-control" multiple id="services-select">
                                                    <option value=""><?php echo e(__('admin.select_services_optional')); ?></option>
                                                    <?php if($coupon->provider_id): ?>
                                                        <?php $__currentLoopData = $coupon->provider->services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($service->id); ?>" <?php echo e(in_array($service->id, $coupon->services->pluck('id')->toArray()) ? 'selected' : ''); ?>><?php echo e($service->name); ?></option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <?php endif; ?>
                                                </select>
                                                <small class="form-text text-muted"><?php echo e(__('admin.leave_empty_to_apply_to_all_services')); ?></small>
                                            </div>
                                        </div>
                                    </div>



                                    <div class="col-12 d-flex justify-content-center mt-3">
                                        <button type="submit" class="btn btn-primary mr-1 mb-1 submit_button"><?php echo e(__('admin.update')); ?></button>
                                        <a href="<?php echo e(url()->previous()); ?>" type="reset" class="btn btn-outline-warning mr-1 mb-1"><?php echo e(__('admin.back')); ?></a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('js'); ?>
    <script src="<?php echo e(asset('admin/app-assets/vendors/js/forms/validation/jqBootstrapValidation.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/js/scripts/forms/validation/form-validation.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/js/scripts/extensions/sweet-alerts.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/vendors/js/pickers/pickadate/picker.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/vendors/js/pickers/pickadate/picker.date.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/js/scripts/pickers/dateTime/pick-a-datetime.js')); ?>"></script>

    <script>
        $(document).on('change','.select2', function () {
            if ($(this).val() == 'ratio') {
                $('.max_discount').prop('readonly', false);
            }else{
                $('.max_discount').prop('readonly', true);
            }
        });
    </script>
    <script>
        $(document).on('keyup','.discount', function () {
            if ($('.select2').val() == 'number') {
                $('.max_discount').val($(this).val());
            }else{
                $('.max_discount').val(null);
            }
        });
    </script>

    
        <?php echo $__env->make('admin.shared.addImage', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    

    
        <?php echo $__env->make('admin.shared.submitEditForm', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    

<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layout.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Workstation\Taswk\sorriso-backend\resources\views/admin/coupons/edit.blade.php ENDPATH**/ ?>