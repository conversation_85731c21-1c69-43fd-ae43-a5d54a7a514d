<?php

namespace App\Http\Resources\Api\Order;

use App\Enums\OrderStatus;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'order_number' => $this->order_number,
            'status' => __('admin.'.$this->current_status),
            'status_enum' => OrderStatus::from($this->current_status),
            'date' => $this->created_at->format('Y-m-d h:i:s'),
            'items' => $this->items->map(function($item) {
                return ['name' => $item->name];
            }),
        ];
    }
}
