<?php

namespace App\Services\Myfatoorah;

use Exception;
use App\Models\User;
use App\Models\Order;
use App\Models\Product;
use App\Services\OrderService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Services\Myfatoorah\PaymentMyfatoorahApiV2;

class OrderPaymentService
{
    protected $myfatoorahApi;
    protected $config;
    protected $orderService;

    public function __construct(OrderService $orderService = null)
    {
        $this->config = config('myfatoorah');
        $this->myfatoorahApi = new PaymentMyfatoorahApiV2(
            $this->config['api_key'],
            $this->config['test_mode'],
            $this->config['log_enabled'] ? $this->config['log_file'] : null
        );
        $this->orderService = $orderService ?? app(OrderService::class);
    }

    /**
     * Create payment invoice for order
     */
    public function createOrderPaymentInvoice(Order $order, User $user, array $options = [])
    {
        try {
            // Ensure proper amount formatting
            $invoiceValue = (float) $order->total;

            // Get clean phone number
            $cleanPhone = $this->cleanPhoneNumber($user->phone ?? '');

            // Ensure we have required data
            if (empty($cleanPhone)) {
                throw new Exception('Valid phone number is required for payment');
            }

            if ($invoiceValue <= 0) {
                throw new Exception('Invalid payment amount');
            }

            $postFields = [
                'NotificationOption' => $this->config['notification_option'],
                'InvoiceValue' => $invoiceValue,
                'CustomerName' => $user->name ?? 'Customer',
                'DisplayCurrencyIso' => $this->config['currency'],
                'MobileCountryCode' => $this->config['country_code'],
                'CustomerMobile' => $cleanPhone,
                'CallBackUrl' => route('payment.webhook'),
                'ErrorUrl' => route('payment.webhook'),
                'Language' => $this->config['language'],
                'CustomerReference' => (string) $order->id, // Use order ID as reference
                'UserDefinedField' => 'order_payment', // Identify this as order payment
                'CustomerEmail' => $user->email ?? '<EMAIL>',
                'InvoiceItems' => $this->buildInvoiceItems($order),
            ];

            // Log the payment request for debugging
            Log::info('Creating MyFatoorah order invoice', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'amount' => $invoiceValue,
                'currency' => $this->config['currency'],
                'phone' => $cleanPhone,
                'test_mode' => $this->config['test_mode'],
                'gateway' => $options['gateway'] ?? 'myfatoorah'
            ]);

            // Get payment gateway
            $gateway = $options['gateway'] ?? 'myfatoorah';

            // Resolve PaymentMethodId if gateway is not 'myfatoorah'
            if ($gateway !== 'myfatoorah') {
                $gateway = $this->resolvePaymentMethodId($gateway, $invoiceValue);
            }

            // Create invoice
            $result = $this->myfatoorahApi->getInvoiceURL($postFields, $gateway, $order->id);
            // Update order with payment reference
            $order->update([
                'payment_reference' => $result['invoiceId'],
            ]);

            return [
                'success' => true,
                'invoice_url' => $result['invoiceURL'],
                'invoice_id' => $result['invoiceId'],
                'order_id' => $order->id,
            ];

        } catch (Exception $e) {
            Log::error('MyFatoorah order payment creation failed', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to create payment invoice: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Build invoice items from order
     */
    private function buildInvoiceItems(Order $order)
    {
        $items = [];

        // Add order items
        foreach ($order->items as $item) {
            $items[] = [
                'ItemName' => $item->item->name ?? 'Product',
                'Quantity' => $item->quantity,
                'UnitPrice' => (float) $item->price,
                'Weight' => 0,
                'Width' => 0,
                'Height' => 0,
                'Depth' => 0,
            ];
        }

        // Add delivery fee if applicable
        if ($order->delivery_fee > 0) {
            $items[] = [
                'ItemName' => 'Delivery Fee',
                'Quantity' => 1,
                'UnitPrice' => (float) $order->delivery_fee,
                'Weight' => 0,
                'Width' => 0,
                'Height' => 0,
                'Depth' => 0,
            ];
        }

        // Add booking fee if applicable
        if ($order->booking_fee > 0) {
            $items[] = [
                'ItemName' => 'Booking Fee',
                'Quantity' => 1,
                'UnitPrice' => (float) $order->booking_fee,
                'Weight' => 0,
                'Width' => 0,
                'Height' => 0,
                'Depth' => 0,
            ];
        }

        // Add home service fee if applicable
        if ($order->home_service_fee > 0) {
            $items[] = [
                'ItemName' => 'Home Service Fee',
                'Quantity' => 1,
                'UnitPrice' => (float) $order->home_service_fee,
                'Weight' => 0,
                'Width' => 0,
                'Height' => 0,
                'Depth' => 0,
            ];
        }

        // Add discount as negative item if applicable
        if ($order->discount_amount > 0) {
            $items[] = [
                'ItemName' => 'Discount' . ($order->coupon_code ? ' (' . $order->coupon_code . ')' : ''),
                'Quantity' => 1,
                'UnitPrice' => -(float) $order->discount_amount, // Negative value for discount
                'Weight' => 0,
                'Width' => 0,
                'Height' => 0,
                'Depth' => 0,
            ];
        }

        // Add loyalty points discount as negative item if applicable
        if ($order->loyalty_points_used > 0) {
            $items[] = [
                'ItemName' => 'Loyalty Points Discount',
                'Quantity' => 1,
                'UnitPrice' => -(float) $order->loyalty_points_used, // Negative value for discount
                'Weight' => 0,
                'Width' => 0,
                'Height' => 0,
                'Depth' => 0,
            ];
        }

        // If no items, add a generic order item
        if (empty($items)) {
            $items[] = [
                'ItemName' => 'Order #' . $order->order_number,
                'Quantity' => 1,
                'UnitPrice' => (float) $order->total,
                'Weight' => 0,
                'Width' => 0,
                'Height' => 0,
                'Depth' => 0,
            ];
        }

        // Calculate items total and ensure it matches order total
        $itemsTotal = collect($items)->sum(function($item) {
            return $item['Quantity'] * $item['UnitPrice'];
        });

        // Check if there's a discrepancy and add adjustment item if needed
        $difference = round($order->total - $itemsTotal, 2);
        if (abs($difference) > 0.01) { // If difference is more than 1 cent
            $items[] = [
                'ItemName' => 'Total Adjustment',
                'Quantity' => 1,
                'UnitPrice' => $difference,
                'Weight' => 0,
                'Width' => 0,
                'Height' => 0,
                'Depth' => 0,
            ];

            // Recalculate total after adjustment
            $itemsTotal = collect($items)->sum(function($item) {
                return $item['Quantity'] * $item['UnitPrice'];
            });
        }

        Log::info('MyFatoorah invoice items breakdown', [
            'order_id' => $order->id,
            'order_total' => $order->total,
            'items_total' => $itemsTotal,
            'difference' => $difference,
            'items_count' => count($items),
            'items' => $items
        ]);

        return $items;
    }

    /**
     * Verify payment status for order
     */
    public function verifyOrderPayment($paymentId, $orderId = null)
    {
        try {
            $responseData = $this->myfatoorahApi->getPaymentStatus($paymentId, 'PaymentId');

            // Convert to array for easier handling
            $responseArray = json_decode(json_encode($responseData), true);

            // Verify this is an order payment
            if ($responseArray['UserDefinedField'] !== 'order_payment') {
                throw new Exception('Invalid payment type');
            }

            // Get order ID from customer reference
            $orderIdFromResponse = $responseArray['CustomerReference'];

            // Verify order ID matches if provided
            if ($orderId && $orderIdFromResponse != $orderId) {
                throw new Exception('Order ID mismatch');
            }

            // Find the order
            $order = Order::find($orderIdFromResponse);
            if (!$order) {
                throw new Exception('Order not found');
            }

            // Check payment status
            if ($responseArray['focusTransaction']['TransactionStatus'] === 'Succss') {
                return $this->handleSuccessfulPayment($order, $responseArray);
            } else {
                return $this->handleFailedPayment($order, $responseArray);
            }

        } catch (Exception $e) {
            Log::error('MyFatoorah order payment verification failed', [
                'payment_id' => $paymentId,
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Payment verification failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Handle successful payment
     */
    private function handleSuccessfulPayment(Order $order, array $responseData)
    {
        return DB::transaction(function () use ($order, $responseData) {
            // Update order status
            $order->update([
                'payment_status' => 'success',
                'current_status' => 'processing',
                'payment_reference' => $responseData['focusTransaction']['PaymentId'],
            ]);

            // Update all suborders to 'processing' and create status records
            foreach ($order->providerSubOrders as $subOrder) {
                $subOrder->updateStatus(
                    'processing',
                    'App\\Models\\User',
                    $order->user_id
                );
            }

            // Create main order status record
            $order->updateStatus(
                'processing',
                'App\\Models\\User',
                $order->user_id
            );

            // Clear the user's cart now that payment is successful
            $user = $order->user;
            if ($user->cart) {
                $user->cart->items()->delete();
                $user->cart->delete();
            }

            // Process loyalty points if applicable
            if (method_exists($user, 'addLoyaltyPoints')) {
                $pointsEarned = $user->calculateLoyaltyPointsEarned($order->total);
                if ($pointsEarned > 0) {
                    $user->addLoyaltyPoints($pointsEarned);
                }
            }

            // CREDIT PROVIDER WALLETS after online payment
            $this->orderService->creditProvidersAfterOnlinePayment($order);

            Log::info('Order payment successful', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'payment_id' => $responseData['focusTransaction']['PaymentId'],
                'amount' => $order->total,
                'cart_cleared' => true
            ]);

            return [
                'success' => true,
                'message' => 'Payment successful',
                'order' => $order->fresh(),
                'payment_data' => $responseData
            ];
        });
    }

    /**
     * Handle failed payment
     */
    private function handleFailedPayment(Order $order, array $responseData)
    {
        return DB::transaction(function () use ($order, $responseData) {
            // Update order status
            $order->update([
                'payment_status' => 'failed',
                'current_status' => 'cancelled',
            ]);

            // Restore product quantities back to stock
            $this->restoreProductQuantities($order);

            Log::warning('Order payment failed', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'payment_status' => $responseData['InvoiceStatus'],
                'error' => $responseData['InvoiceError'] ?? 'Unknown error',
                'quantities_restored' => true
            ]);

            return [
                'success' => false,
                'message' => 'Payment failed: ' . ($responseData['InvoiceError'] ?? 'Unknown error'),
                'order' => $order->fresh(),
                'payment_data' => $this->formatPaymentDataForResponse($responseData)
            ];
        });
    }

    /**
     * Restore product quantities back to stock when payment fails
     */
    private function restoreProductQuantities(Order $order)
    {
        foreach ($order->items as $orderItem) {
            if ($orderItem->isProduct()) {
                $product = Product::where('id', $orderItem->item_id)
                    ->lockForUpdate()
                    ->first();

                if ($product) {
                    // Restore quantity atomically
                    Product::where('id', $product->id)
                        ->update(['quantity' => DB::raw('quantity + ' . $orderItem->quantity)]);

                    Log::info('Product quantity restored after payment failure', [
                        'product_id' => $product->id,
                        'product_name' => $product->name,
                        'restored_quantity' => $orderItem->quantity,
                        'new_quantity' => $product->quantity + $orderItem->quantity,
                        'order_id' => $order->id,
                        'order_number' => $order->order_number
                    ]);
                }
            }
        }
    }

    /**
     * Format payment data for API response with proper currency display
     */
    private function formatPaymentDataForResponse(array $responseData)
    {
        // Create a clean response with SAR values for display
        $formattedData = $responseData;

        // Ensure currency fields show SAR
        if (isset($formattedData['InvoiceDisplayValue'])) {
            // Extract the numeric value and format as SAR
            $numericValue = $formattedData['InvoiceValue'] ?? 0;
            $formattedData['InvoiceDisplayValue'] = number_format($numericValue, 3) . ' SAR';
        }

        // Update transaction currency display
        if (isset($formattedData['InvoiceTransactions']) && is_array($formattedData['InvoiceTransactions'])) {
            foreach ($formattedData['InvoiceTransactions'] as &$transaction) {
                $transaction['PaidCurrency'] = 'SAR';
                $transaction['Currency'] = 'SAR';

                // Format transaction value display
                if (isset($transaction['TransationValue'])) {
                    $transaction['TransationValueDisplay'] = number_format($transaction['TransationValue'], 3) . ' SAR';
                }
                if (isset($transaction['DueValue'])) {
                    $transaction['DueValueDisplay'] = number_format($transaction['DueValue'], 3) . ' SAR';
                }
            }
        }

        // Update invoice items currency display
        if (isset($formattedData['InvoiceItems']) && is_array($formattedData['InvoiceItems'])) {
            foreach ($formattedData['InvoiceItems'] as &$item) {
                if (isset($item['UnitPrice'])) {
                    $item['UnitPriceDisplay'] = number_format($item['UnitPrice'], 3) . ' SAR';
                }
            }
        }

        return $formattedData;
    }

    /**
     * Resolve PaymentMethodId from gateway name
     */
    private function resolvePaymentMethodId($gateway, $invoiceValue)
    {
        try {
            // Map gateway names to PaymentMethodCode for lookup
            $gatewayCodeMap = [
                'visa' => 'visa',
                'mada' => 'mada',
                'applepay' => 'ap',
                'googlepay' => 'gp',
                'mastercard' => 'mc',
                'amex' => 'ae'
            ];

            $gatewayCode = $gatewayCodeMap[$gateway] ?? $gateway;

            // Get payment method object using PaymentMethodCode
            $paymentMethod = $this->myfatoorahApi->getPaymentMethod(
                $gatewayCode,
                'PaymentMethodCode',
                $invoiceValue,
                $this->config['currency']
            );

            Log::info('Resolved PaymentMethodId', [
                'gateway' => $gateway,
                'gateway_code' => $gatewayCode,
                'payment_method_id' => $paymentMethod->PaymentMethodId,
                'payment_method_name' => $paymentMethod->PaymentMethodEn
            ]);

            return $paymentMethod->PaymentMethodId;

        } catch (Exception $e) {
            Log::warning('Failed to resolve PaymentMethodId, falling back to myfatoorah', [
                'gateway' => $gateway,
                'error' => $e->getMessage()
            ]);

            // Fallback to myfatoorah if resolution fails
            return 'myfatoorah';
        }
    }

    /**
     * Clean phone number for MyFatoorah
     */
    private function cleanPhoneNumber($phone)
    {
        if (empty($phone)) {
            return '';
        }

        // Remove all non-numeric characters
        $phone = preg_replace('/[^0-9]/', '', $phone);

        // Remove leading zeros
        $phone = ltrim($phone, '0');

        // Remove country code if present
        if (str_starts_with($phone, '966')) {
            $phone = substr($phone, 3);
        }

        // Ensure we have a valid Saudi phone number (9 digits starting with 5)
        if (strlen($phone) === 9 && str_starts_with($phone, '5')) {
            return $phone;
        }

        // If phone doesn't match expected format, return empty (will trigger validation error)
        return '';
    }

    /**
     * Get available payment gateways for orders
     */
    public function getAvailableGateways($amount = 0)
    {
        try {
            $gateways = $this->myfatoorahApi->getVendorGateways($amount, $this->config['currency']);

            // Filter to only order-supported gateways
            $supportedGateways = collect($gateways)->filter(function ($gateway) {
                return in_array($gateway->PaymentMethodCode, $this->config['course_gateways']); // Reuse same gateways
            });

            return [
                'success' => true,
                'gateways' => $supportedGateways->values()->toArray()
            ];

        } catch (Exception $e) {
            Log::error('Failed to get MyFatoorah order gateways', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to get payment gateways',
                'gateways' => []
            ];
        }
    }
}
