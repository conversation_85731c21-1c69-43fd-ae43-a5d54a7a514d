

<?php $__env->startSection('css'); ?>
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/index_page.css')); ?>">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<?php if (isset($component)) { $__componentOriginal781089cd478f3e09d520a65f160df974 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal781089cd478f3e09d520a65f160df974 = $attributes; } ?>
<?php $component = App\View\Components\Admin\Table::resolve(['datefilter' => 'true','order' => 'true','extrabuttons' => 'true','searchArray' => [
        'order_number' => [
            'input_type' => 'text',
            'input_name' => __('admin.order_number'),
        ],
        'current_status' => [
            'input_type' => 'select',
            'rows' => [
                'pending_payment' => [
                    'name' => __('admin.pending_payment'),
                    'id' => 'pending_payment',
                ],
                'processing' => [
                    'name' => __('admin.processing'),
                    'id' => 'processing',
                ],
                'confirmed' => [
                    'name' => __('admin.confirmed'),
                    'id' => 'confirmed',
                ],
                'completed' => [
                    'name' => __('admin.completed'),
                    'id' => 'completed',
                ],
                'cancelled' => [
                    'name' => __('admin.cancelled'),
                    'id' => 'cancelled',
                ],
              
            ],
            'input_name' => __('admin.order_status'),
        ],
        'payment_status' => [
            'input_type' => 'select',
            'rows' => [
                'pending' => [
                    'name' => __('admin.pending'),
                    'id' => 'pending',
                ],
                'success' => [
                    'name' => __('admin.success'),
                    'id' => 'success',
                ],
                'failed' => [
                    'name' => __('admin.failed'),
                    'id' => 'failed',
                ],
                'refunded' => [
                    'name' => __('admin.refunded'),
                    'id' => 'refunded',
                ],
            ],
            'input_name' => __('admin.payment_status'),
        ],
        'user_id' => [
            'input_type' => 'select',
            'rows' => \App\Models\User::get(['id', 'name'])->map(function($user) {
                return ['id' => $user->id, 'name' => $user->name];
            })->toArray(),
            'input_name' => __('admin.user'),
        ],
    ]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin.table'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Admin\Table::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('extrabuttonsdiv', null, []); ?> 
        
     <?php $__env->endSlot(); ?>

     <?php $__env->slot('tableContent', null, []); ?> 
        <div class="table_content_append card">
            
        </div>
     <?php $__env->endSlot(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal781089cd478f3e09d520a65f160df974)): ?>
<?php $attributes = $__attributesOriginal781089cd478f3e09d520a65f160df974; ?>
<?php unset($__attributesOriginal781089cd478f3e09d520a65f160df974); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal781089cd478f3e09d520a65f160df974)): ?>
<?php $component = $__componentOriginal781089cd478f3e09d520a65f160df974; ?>
<?php unset($__componentOriginal781089cd478f3e09d520a65f160df974); ?>
<?php endif; ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
    <script src="<?php echo e(asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/js/scripts/extensions/sweet-alerts.js')); ?>"></script>
    <?php echo $__env->make('admin.shared.deleteAll', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('admin.shared.deleteOne', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('admin.shared.filter_js' , [ 'index_route' => url('admin/bank-transfer-orders')], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <script>
        // Verify Transfer
        $(document).on('click', '.verify-transfer', function() {
            const orderId = $(this).data('order-id');
            
            Swal.fire({
                title: '<?php echo e(__("admin.verify_transfer")); ?>',
                text: '<?php echo e(__("admin.are_you_sure_verify_transfer")); ?>',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: '<?php echo e(__("admin.verify")); ?>',
                cancelButtonText: '<?php echo e(__("admin.cancel")); ?>',
                input: 'textarea',
                inputPlaceholder: '<?php echo e(__("admin.notes_optional")); ?>',
                inputAttributes: {
                    'aria-label': '<?php echo e(__("admin.notes")); ?>'
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: `/admin/bank-transfer-orders/${orderId}/verify-transfer`,
                        type: 'POST',
                        data: {
                            _token: '<?php echo e(csrf_token()); ?>',
                            notes: result.value
                        },
                        success: function(response) {
                            if (response.success) {
                                Swal.fire('<?php echo e(__("admin.success")); ?>', response.message, 'success');
                                location.reload();
                            } else {
                                Swal.fire('<?php echo e(__("admin.error")); ?>', response.message, 'error');
                            }
                        },
                        error: function(xhr) {
                            Swal.fire('<?php echo e(__("admin.error")); ?>', '<?php echo e(__("admin.something_went_wrong")); ?>', 'error');
                        }
                    });
                }
            });
        });

        // Reject Transfer
        $(document).on('click', '.reject-transfer', function() {
            const orderId = $(this).data('order-id');
            
            Swal.fire({
                title: '<?php echo e(__("admin.reject_transfer")); ?>',
                text: '<?php echo e(__("admin.are_you_sure_reject_transfer")); ?>',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: '<?php echo e(__("admin.reject")); ?>',
                cancelButtonText: '<?php echo e(__("admin.cancel")); ?>',
                input: 'textarea',
                inputPlaceholder: '<?php echo e(__("admin.rejection_reason")); ?>',
                inputAttributes: {
                    'aria-label': '<?php echo e(__("admin.reason")); ?>'
                },
                inputValidator: (value) => {
                    if (!value) {
                        return '<?php echo e(__("admin.rejection_reason_required")); ?>'
                    }
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: `/admin/bank-transfer-orders/${orderId}/reject-transfer`,
                        type: 'POST',
                        data: {
                            _token: '<?php echo e(csrf_token()); ?>',
                            reason: result.value
                        },
                        success: function(response) {
                            if (response.success) {
                                Swal.fire('<?php echo e(__("admin.success")); ?>', response.message, 'success');
                                location.reload();
                            } else {
                                Swal.fire('<?php echo e(__("admin.error")); ?>', response.message, 'error');
                            }
                        },
                        error: function(xhr) {
                            Swal.fire('<?php echo e(__("admin.error")); ?>', '<?php echo e(__("admin.something_went_wrong")); ?>', 'error');
                        }
                    });
                }
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layout.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Workstation\Taswk\sorriso-backend\resources\views/admin/bank_transfer_orders/index.blade.php ENDPATH**/ ?>