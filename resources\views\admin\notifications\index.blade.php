@extends('admin.layout.master')

@section('css')
    <link rel="stylesheet" type="text/css" href="{{asset('admin/app-assets/css-rtl/plugins/forms/validation/form-validation.css')}}">
    <link rel="stylesheet" type="text/css" href="{{asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css')}}">
@endsection

@section('content')
<section class="users-edit">
    <div class="card">
        <div class="card-content">
            <div class="card-body">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="feather icon-bell mr-25"></i>{{__('admin.send_notification')}}
                    </h4>
                </div>
                <div class="tab-content">
                    <div class="col-12">
                        <ul class="nav nav-tabs  mb-3">
                            @foreach (languages() as $lang)
                                <li class="nav-item">
                                    <a class="nav-link @if($loop->first) active @endif"  data-toggle="pill" href="#first_{{$lang}}" aria-expanded="true">{{  __('admin.data') }} {{ $lang }}</a>
                                </li>
                            @endforeach
                        </ul>
                    </div>

                    <div class="notification-form-container">
                        <form action="{{route('admin.notifications.send')}}" method="POST" enctype="multipart/form-data" class="notify-form" novalidate>
                            @csrf
                            <input type="hidden" name="type" value="notify">
                            <div class="row">

                                <div class="col-12">
                                <div class="tab-content">
                                @foreach (languages() as $lang)
                                <div role="tabpanel" class="tab-pane fade @if($loop->first) show active @endif " id="first_{{$lang}}" aria-labelledby="first_{{$lang}}" aria-expanded="true">

                                    <div class="col-md-12 col-6">
                                        <div class="form-group">
                                            <label for="first-name-column">{{__('admin.the_title')}} {{ $lang }}</label>
                                            <div class="controls">
                                                <input type="text" name="title[{{ $lang }}]" class="form-control" required data-validation-required-message="{{__('admin.this_field_is_required')}}">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12 col-12">
                                        <div class="form-group">
                                            <label for="first-name-column">{{__('admin.the_message')}} {{ $lang }}</label>
                                            <div class="controls">
                                                <textarea name="body[{{ $lang }}]" class="form-control" cols="30" rows="10" required data-validation-required-message="{{__('admin.this_field_is_required')}}"></textarea>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                @endforeach
                            </div>
                        </div>

                                <div class="col-md-12 col-12">
                                    <div class="form-group">
                                        <label for="first-name-column">{{__('admin.send_to')}}</label>
                                        <div class="controls">
                                            <select name="user_type" required data-validation-required-message="{{__('admin.this_field_is_required')}}" class="select2 form-control" >
                                                <option value>{{__('admin.Select_the_senders_category')}}</option>
                                                <option  value="all">{{__('admin.all_users')}}</option>
                                                <option  value="clients">{{__('admin.clients')}}</option>
                                                <option  value="provider">{{__('admin.provider')}}</option>
                                                <option  value="admins">{{__('admin.admins')}}</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary send-notify-button" >{{__('admin.send')}}</button>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="tab-pane " id="sms" aria-labelledby="sms-tab" role="tabpanel">
                        <form action="{{route('admin.notifications.send')}}" method="POST" enctype="multipart/form-data" class="notify-form" novalidate >
                            @csrf
                            <input type="hidden" name="type" value="sms">
                            <div class="row">
                                <div class="col-md-12 col-12">
                                    <div class="form-group">
                                        <label for="first-name-column">{{__('admin.text_of_message')}}</label>
                                        <div class="controls">
                                            <textarea name="body" class="form-control" cols="30" rows="10" required data-validation-required-message="{{__('admin.this_field_is_required')}}" ></textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12 col-12">
                                    <div class="form-group">
                                        <label for="first-name-column">{{__('admin.send_to')}}</label>
                                        <div class="controls">
                                            <select name="user_type" class="select2 form-control" required data-validation-required-message="{{__('admin.this_field_is_required')}}" >
                                                <option value>{{__('admin.Select_the_senders_category')}}</option>
                                                <option  value="all">{{__('admin.all_users')}}</option>
                                                <option  value="clients">{{__('admin.clients')}}</option>
                                                <option  value="provider">{{__('admin.provider')}}</option>
                                                <option  value="admins">{{__('admin.admins')}}</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary send-notify-button" >{{__('admin.send')}}</button>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="tab-pane " id="email" aria-labelledby="email-tab" role="tabpanel">
                        <form action="{{route('admin.notifications.send')}}" method="POST" enctype="multipart/form-data" class="notify-form" novalidate >
                            @csrf
                            <input type="hidden" name="type" value="email">
                            <div class="row">
                                <div class="col-md-12 col-12">
                                    <div class="form-group">
                                        <label for="first-name-column">{{__('admin.email_content')}}</label>
                                        <div class="controls">
                                            <textarea name="body" class="form-control" cols="30" rows="10" required data-validation-required-message="{{__('admin.this_field_is_required')}}"></textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12 col-12">
                                    <div class="form-group">
                                        <label for="first-name-column">{{__('admin.send_to')}}</label>
                                        <div class="controls">
                                            <select name="user_type" class="select2 form-control" required data-validation-required-message="{{__('admin.this_field_is_required')}}" >
                                                <option value>{{__('admin.Select_the_senders_category')}}</option>
                                                <option  value="all">{{__('admin.all_users')}}</option>
                                                <option  value="clients">{{__('admin.clients')}}</option>
                                                <option  value="provider">{{__('admin.provider')}}</option>
                                                <option  value="admins">{{__('admin.admins')}}</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary send-notify-button" >{{__('admin.send')}}</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection


@section('js')

<script src="{{asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js')}}"></script>
<script src="{{asset('admin/app-assets/js/scripts/extensions/sweet-alerts.js')}}"></script>
<script src="{{asset('admin/app-assets/vendors/js/forms/validation/jqBootstrapValidation.js')}}"></script>
<script src="{{asset('admin/app-assets/js/scripts/forms/validation/form-validation.js')}}"></script>
<script>
$(document).ready(function(){
    $(document).on('submit','.notify-form',function(e){
        e.preventDefault();
        var form = $(this);
        var url = form.attr('action');
        var isValid = true;

        // إزالة رسائل الخطأ السابقة
        form.find('.text-danger').remove();
        form.find('.border-danger').removeClass('border-danger');

        // التحقق من الحقول المطلوبة
        form.find('[required]').each(function() {
            if ($(this).val() === '' || $(this).val() === null) {
                $(this).addClass('border-danger');
                // Place error after the input/textarea
                $(this).after(`<span class="mt-1 text-danger validation-message">${$(this).attr('data-validation-required-message') || 'هذا الحقل مطلوب'}</span>`);
                isValid = false;

                // التمرير إلى الحقل الفارغ
                if (isValid === false) {
                    $('html, body').animate({
                        scrollTop: $(this).offset().top - 100
                    }, 500);
                }
            }
        });

        // التحقق من حقول اللغات (لنموذج الإشعارات)
        if (form.find('input[name="type"]').val() === 'notify') {
            form.find('textarea[name^="body["]').each(function() {
                if ($(this).val().trim() === '') {
                    $(this).addClass('border-danger');
                    $(this).after(`<span class="mt-1 text-danger validation-message">{{__('admin.this_field_is_required')}}</span>`);
                    isValid = false;
                }
            });
        }

        if (!isValid) {
            return false;
        }

        // إرسال البيانات إذا كانت صالحة
        $.ajax({
            url: url,
            method: 'post',
            data: new FormData(form[0]),
            dataType:'json',
            processData: false,
            contentType: false,
            beforeSend: function(){
                $(".send-notify-button").html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>').attr('disabled',true);
            },
            success: (response)=>{
                $(".text-danger").remove();
                $('.store input').removeClass('border-danger');
                $(".send-notify-button").html("{{__('admin.send')}}").attr('disabled',false);
                Swal.fire({
                    position: 'top-start',
                    type: 'success',
                    title: '{{__('admin.send_successfully')}}',
                    showConfirmButton: false,
                    timer: 1500,
                    confirmButtonClass: 'btn btn-primary',
                    buttonsStyling: false,
                });
                form.trigger("reset");
            },
            error: function (xhr) {
                $(".send-notify-button").html("{{__('admin.send')}}").attr('disabled',false);
                $(".text-danger").remove();
                $('.store input').removeClass('border-danger');
                $('.store textarea').removeClass('border-danger');

                if (xhr.responseJSON && xhr.responseJSON.errors) {
                    $.each(xhr.responseJSON.errors, function(key, value) {
                        var errorMessage = Array.isArray(value) ? value[0] : value;

                        // Try both dot and bracket notation
                        var fieldSelector = '[name="' + key + '"]';
                        var bracketKey = key.replace(/\.(\w+)/g, '[$1]');
                        var field = form.find(fieldSelector);
                        if (field.length === 0) {
                            field = form.find('[name="' + bracketKey + '"]');
                        }
                        if (field.length > 0) {
                            field.addClass('border-danger');
                            // Place error after the input/textarea
                            field.after(`<span class="text-danger d-block mt-1 validation-message">${errorMessage}</span>`);
                        } else {
                            form.prepend(`<div class="alert alert-danger mt-2">${errorMessage}</div>`);
                        }
                    });
                }
            },
        });
    });

    // إزالة رسائل الخطأ عند التركيز على الحقل
    $(document).on('focus', 'input, textarea, select', function() {
        $(this).removeClass('border-danger');
        $(this).closest('.form-group').find('.validation-message').remove();
    });
});
</script>
@endsection