<?php

return [
  'types_by_user'           => 'بواسطة العميل',
  'types_by_provider'       => 'بواسطة مقدم الخدمة',
  'status_new'              => 'جديد',
  'status_accepted'         => 'تم الموافقة',
  'status_refused'          => 'مرفوض',
  'status_cancel'           => 'ملغى',
  'status_cancelled'        => 'ملغى',
  'status_finished'         => 'منتهى',
  'status_in_progress'      => 'قيد التنفيذ',
  'status_delivered'        => 'تم التوصيل',
  'status_pending'          => 'معلق',
  'pay_type_undefined'      => 'غير محدد',
  'pay_type_mada'           => 'نقدا (مدى)',
  'pay_type_cod'            => 'الدفع عند الاستلام',
  'pay_type_visa'           => 'فيزا',
  'pay_type_applepay'       => 'آبل باي',
  'pay_type_googlepay'      => 'جوجل باي',
  'pay_type_tabby'          => 'تابي',
  'pay_type_tamara'         => 'تمارا',
  'pay_status_pending'      => 'لم يتم',
  'pay_status_downpayment'  => 'عربون',
  'pay_status_done'         => 'تم السداد',
  'pay_status_returned'     => 'تم الارجاع',

  // Order Status Values
  'new'                     => 'جديد',
  'accepted'                => 'تم الموافقة',
  'in_progress'             => 'قيد التنفيذ',
  'delivered'               => 'تم التوصيل',
  'cancelled'               => 'ملغى',
  'pending_payment'         => 'في انتظار الدفع',
  'processing'              => 'قيد المعالجة',
  'confirmed'               => 'مؤكد',
  'completed'               => 'مكتمل',
  'partially_cancelled'     => 'ملغى جزئياً',
  'pending_verification'    => 'في انتظار التحقق',
  'request_cancel'          => 'طلب إلغاء',

  // Error messages
  'pending_payment_exists'  => 'لديك طلب في انتظار الدفع. يرجى إكمال الدفع أو انتظار انتهاء صلاحيته قبل إنشاء طلب جديد.',
];