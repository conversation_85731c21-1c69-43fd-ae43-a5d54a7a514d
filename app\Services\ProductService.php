<?php

namespace App\Services;

use App\Models\Product;
use Illuminate\Support\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class ProductService
{
    public function getAllProducts(array $filters = [])
    {
        $query = Product::with(['provider', 'category'])
            ->when($filters['category_id'] ?? null, function ($q, $categoryId) {
                $q->where('product_category_id', $categoryId);
            })
            ->whereHas('provider', function ($providerQuery) {
                $providerQuery->where('status', 'accepted')
                    ->whereHas('user', function ($userQuery) {
                        $userQuery->where('is_active', true);
                    });
            });

        // Sorting logic
        $sort = $filters['sort'] ?? 'new_to_old';
        if ($sort === 'old_to_new') {
            $query->orderBy('created_at', 'asc');
        } else {
            $query->orderBy('created_at', 'desc');
        }
    
        return $query->get();
    }
    



    public function getProviderProducts(int $providerId): Collection
    {
        return Product::where('provider_id', $providerId)
            ->latest()
            ->get();
    }

    public function getProductById(int $id)
    {
        return Product::with(['provider', 'category' , 'rates'])->find($id);
    }

    public function createProduct(array $data): Product
    {
        $data['provider_id'] = auth()->user()->provider->id;
        $product = Product::create($data);
        if(isset($data['images'])) {
            $product->clearMediaCollection('product-images');
            foreach ($data['images'] as $image) {
                $product->addMedia($image)->toMediaCollection('product-images');

            }

        }
        return $product;
    }

    public function updateProduct(Product $product, array $data): bool
    {
        return $product->update($data);
    }

    public function deleteProduct(Product $product): bool
    {
        return $product->delete();
    }

    public function restoreProduct(int $id): bool
    {
        return Product::withTrashed()->where('id', $id)->restore();
    }

    public function toggleProductStatus(Product $product): bool
    {
        return $product->update(['is_active' => !$product->is_active]);
    }


    public function getProviderProductsStats(int $providerId): array
    {
        $totalProducts = Product::forProvider($providerId)->count();
        $activeProducts = Product::forProvider($providerId)->active()->count();
        $inactiveProducts = $totalProducts - $activeProducts;

        return [
            'total_Products' => $totalProducts,
            'active_Products' => $activeProducts,
            'inactive_Products' => $inactiveProducts,
        ];
    }
}