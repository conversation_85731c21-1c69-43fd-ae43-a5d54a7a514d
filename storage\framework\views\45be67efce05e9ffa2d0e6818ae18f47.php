<div class="position-relative">
    
    
    

    
 <table class="table" id="tab">
    <thead>
        <tr>
            <th><?php echo e(__('admin.order_number')); ?></th>
            <th><?php echo e(__('admin.user')); ?></th>
            <th><?php echo e(__('admin.payment_status')); ?></th>
            <th><?php echo e(__('admin.status')); ?></th>
            <th><?php echo e(__('admin.final_total')); ?></th>
            <th><?php echo e(__('admin.created_at')); ?></th>
            <th><?php echo e(__('admin.control')); ?></th>
        </tr>
    </thead>
    <tbody>
        <?php $__currentLoopData = $orders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tr>
                <td>
                    <span class="badge badge-info"><?php echo e($order->order_number ?? $order->order_num); ?></span>
                </td>
                <td>
                    <?php if($order->user): ?>
                        <div>
                            <strong><?php echo e($order->user->name); ?></strong><br>
                            <small class="text-muted"><?php echo e($order->user->phone); ?></small>
                        </div>
                    <?php else: ?>
                        <span class="text-muted">-</span>
                    <?php endif; ?>
                </td>

            
                <td>
                    <?php
                        $statusColors = [
                            'pending' => 'warning',
                            'paid' => 'success',
                            'failed' => 'danger',
                            'pending_verification' => 'info',
                            'refunded' => 'secondary'
                        ];
                        $color = $statusColors[$order->payment_status] ?? 'secondary';
                    ?>
                    <span class="badge badge-<?php echo e($color); ?>">
                        <?php echo e(ucfirst(str_replace('_', ' ', __('admin.' . $order->payment_status)))); ?>

                    </span>
                </td>
                <td>
                    <?php
                        $orderStatusColors = [
                            'pending_payment' => 'warning',
                            'processing' => 'info',
                            'confirmed' => 'primary',
                            'completed' => 'success',
                            'cancelled' => 'danger',
                            'pending_verification' => 'secondary'
                        ];
                        $orderColor = $orderStatusColors[$order->current_status] ?? 'secondary';
                    ?>
                    <span class="badge badge-<?php echo e($orderColor); ?>">
                        <?php echo e(ucfirst(str_replace('_', ' ', __('admin.' . $order->current_status)))); ?>

                    </span>
                    <?php if($order->current_status === 'cancelled' && $order->cancelReason): ?>
                        <br>
                        <?php
                            $reasonData = json_decode($order->cancelReason->reason, true);
                            $reasonText = $reasonData[app()->getLocale()] ?? $reasonData['en'] ?? 'Unknown';
                        ?>
                        <small class="text-danger" title="<?php echo e($reasonText); ?>">
                            <i class="feather icon-info mr-1"></i>
                            <?php echo e(Str::limit($reasonText, 30)); ?>

                        </small>
                    <?php endif; ?>
                </td>
                <td>
                    <strong><?php echo e(number_format($order->final_total ?? $order->total, 2)); ?> <?php echo e(__('admin.sar')); ?></strong>
                    <?php if($order->services_total > 0 || $order->products_total > 0): ?>
                        <br>
                        <small class="text-muted">
                            <?php if($order->services_total > 0): ?>
                                <?php echo e(__('admin.services')); ?>: <?php echo e(number_format($order->services_total, 2)); ?>

                            <?php endif; ?>
                            <?php if($order->products_total > 0): ?>
                                <?php if($order->services_total > 0): ?> | <?php endif; ?>
                                <?php echo e(__('admin.products')); ?>: <?php echo e(number_format($order->products_total, 2)); ?>

                            <?php endif; ?>
                        </small>
                    <?php endif; ?>
                </td>
                <td>
                    <div><?php echo e($order->created_at->format('d/m/Y')); ?></div>
                    <small class="text-muted"><?php echo e($order->created_at->format('H:i')); ?></small>
                </td>
                <td class="order-action">
                    <a href="<?php echo e(route('admin.bank_transfer_orders.show', ['id' => $order->id])); ?>"
                       class="btn btn-warning btn-sm p-1" title="<?php echo e(__('admin.view_details')); ?>">
                        <i class="feather icon-eye"></i>
                    </a>

                    <?php if($order->bankTransfer && $order->bankTransfer->status === 'pending'): ?>
                        <button class="btn btn-success btn-sm p-1 verify-transfer" data-order-id="<?php echo e($order->id); ?>" title="<?php echo e(__('admin.verify_transfer')); ?>">
                            <i class="feather icon-check"></i>
                        </button>
                        <button class="btn btn-danger btn-sm p-1 reject-transfer" data-order-id="<?php echo e($order->id); ?>" title="<?php echo e(__('admin.reject_transfer')); ?>">
                            <i class="feather icon-x"></i>
                        </button>
                    <?php endif; ?>
                </td>
            </tr>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </tbody>
</table>


    
    <?php if($orders->count() == 0): ?>
        <div class="d-flex flex-column w-100 align-center mt-4">
            <img src="<?php echo e(asset('admin/app-assets/images/pages/404.png')); ?>" alt="">
            <span class="mt-2" style="font-family: cairo"><?php echo e(__('admin.there_are_no_matches_matching')); ?></span>
        </div>
    <?php endif; ?>
    

</div>


<?php if($orders->count() > 0 && $orders instanceof \Illuminate\Pagination\AbstractPaginator ): ?>
    <div class="d-flex justify-content-center mt-3">
        <?php echo e($orders->links()); ?>

    </div>
<?php endif; ?>

<?php /**PATH D:\Workstation\Taswk\sorriso-backend\resources\views/admin/bank_transfer_orders/table.blade.php ENDPATH**/ ?>