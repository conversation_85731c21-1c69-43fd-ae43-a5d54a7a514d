<?php

namespace App\Jobs;

use App\Models\Order;
use App\Models\Admin;
use App\Jobs\AdminNotify;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Carbon\Carbon;

class CheckPaymentDelays implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Get orders with pending payment that are older than the configured delay time
        $delayThresholdHours = config('app.payment_delay_threshold_hours', 24); // Default 24 hours
        $thresholdTime = Carbon::now()->subHours($delayThresholdHours);

        $delayedOrders = Order::where('payment_status', Order::PAY_STATUS_PENDING)
            ->where('current_status', 'pending_payment')
            ->where('created_at', '<=', $thresholdTime)
            ->whereDoesntHave('orderNotifications', function($query) {
                $query->where('type', 'payment_delay')
                    ->where('created_at', '>=', Carbon::now()->subDay());
            })
            ->with(['user', 'provider'])
            ->get();

        if ($delayedOrders->count() > 0) {
            $admins = Admin::where('is_active', true)->get();
            
            foreach ($delayedOrders as $order) {
                // Create notification data
                $notificationData = [
                    'title' => 'تأخر في دفع الطلب',
                    'body' => "الطلب رقم {$order->order_number} للعميل {$order->user->name} متأخر في الدفع منذ أكثر من {$delayThresholdHours} ساعة",
                    'type' => 'payment_delay',
                    'order_id' => $order->id,
                    'user_id' => $order->user_id,
                    'provider_id' => $order->provider_id,
                ];

                // Send notification to admins
                dispatch(new AdminNotify($admins, $notificationData));

                // Mark order as notified to avoid duplicate notifications
                $order->orderNotifications()->create([
                    'type' => 'payment_delay',
                    'data' => $notificationData,
                    'sent_at' => Carbon::now(),
                ]);
            }
        }
    }
}
