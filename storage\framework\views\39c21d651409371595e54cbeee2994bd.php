<div class="position-relative">
    
    
    
    
    
    <table class="table " id="tab">
        <thead>
            <tr>
                <th>
                    <label class="container-checkbox">
                        <input type="checkbox" value="value1" name="name1" id="checkedAll">
                        <span class="checkmark"></span>
                    </label>
                </th>
                <th><?php echo e(__('admin.user_name')); ?></th>
                <th><?php echo e(__('admin.rate_type')); ?></th>
                <th><?php echo e(__('admin.rateable_name')); ?></th>
                <th><?php echo e(__('admin.status')); ?></th>
                <th><?php echo e(__('admin.control')); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $rates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $rate): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr class="delete_row">
                    <td class="text-center">
                        <label class="container-checkbox">
                        <input type="checkbox" class="checkSingle" id="<?php echo e($rate->id); ?>">
                        <span class="checkmark"></span>
                        </label>
                    </td>
                    <td><?php echo e($rate->user->name ?? 'N/A'); ?></td>
                    <td>
                        <?php
                            $rateType = class_basename($rate->rateable_type);
                        ?>
                        <span class="badge badge-info"><?php echo e(__('admin.' . strtolower($rateType))); ?></span>
                    </td>
                    <td><?php echo e($rate->rateable->commercial_name ?? $rate->rateable->name ?? 'N/A'); ?></td>
                   
                    <td>
                        <?php if($rate->status == 'rejected'): ?>
                        <span class="btn btn-sm round btn-outline-danger">
                            <?php echo e(__('admin.rejected')); ?> <i class="la la-close font-medium-2"></i>
                        </span>
                        <?php elseif($rate->status == 'approved'): ?>
                        <span class="btn btn-sm round btn-outline-success">
                            <?php echo e(__('admin.approved')); ?> <i class="la la-check font-medium-2"></i>
                        </span>
                        <?php elseif($rate->status == 'pending'): ?>
                        <span class="btn btn-sm round btn-outline-warning">
                                <?php echo e(__('admin.pending')); ?> <i class="la la-clock font-medium-2"></i>
                        </span>
                        <?php endif; ?>
                    </td>
                    
                    <td class="product-action">
                        <span class="text-primary"><a href="<?php echo e(route('admin.rates.show', ['id' => $rate->id])); ?>" class="btn btn-warning btn-sm p-1" title="<?php echo e(__('admin.show')); ?>"><i class="feather icon-eye"></i></a></span>
                        <span class="delete-row btn btn-danger btn-sm p-1" data-url="<?php echo e(url('admin/rates/' . $rate->id)); ?>" title="<?php echo e(__('admin.delete')); ?>"><i class="feather icon-trash"></i></span>
                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>
    
    
    <?php if($rates->count() == 0): ?>
        <div class="d-flex flex-column w-100 align-center mt-4">
            <img src="<?php echo e(asset('admin/app-assets/images/pages/404.png')); ?>" alt="">
            <span class="mt-2" style="font-family: cairo"><?php echo e(__('admin.there_are_no_matches_matching')); ?></span>
        </div>
    <?php endif; ?>
    

</div>

<?php if($rates->count() > 0 && $rates instanceof \Illuminate\Pagination\AbstractPaginator ): ?>
    <div class="d-flex justify-content-center mt-3">
        <?php echo e($rates->links()); ?>

    </div>
<?php endif; ?>


<?php /**PATH D:\Workstation\Taswk\sorriso-backend\resources\views/admin/rates/table.blade.php ENDPATH**/ ?>