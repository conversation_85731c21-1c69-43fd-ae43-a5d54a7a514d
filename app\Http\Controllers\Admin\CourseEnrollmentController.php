<?php

namespace App\Http\Controllers\Admin;

use App\Models\CourseEnrollment;
use App\Models\Course;
use App\Models\User;
use App\Traits\Report;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Log;
use PDF;

class CourseEnrollmentController extends Controller
{
    use Report;

    public function index($id = null)
    {
        if (request()->ajax()) {
            $searchArray = request()->searchArray ?? [];

            // If no search criteria or all empty, get all enrollments
            if (empty($searchArray) || empty(array_filter($searchArray, function($value) {
                return $value !== '' && $value !== null;
            }))) {
                $enrollments = CourseEnrollment::with(['user', 'course'])->orderBy('enrolled_at', 'DESC')->paginate(30);
            } else {
                $enrollments = CourseEnrollment::with(['user', 'course'])->search($searchArray)->paginate(30);
            }

            $html = view('admin.course_enrollments.table', compact('enrollments'))->render();
            return response()->json(['html' => $html]);
        }

        // Get courses and users for search dropdowns
        $courses = Course::select('id', 'name')->get();
        $users = User::select('id', 'name')->get();

        return view('admin.course_enrollments.index', compact('courses', 'users'));
    }

    public function show($id)
    {
        $enrollment = CourseEnrollment::with(['user', 'course', 'verifiedBy', 'rejectedBy'])->findOrFail($id);
        return view('admin.course_enrollments.show', compact('enrollment'));
    }

    public function downloadPdf($id)
    {
        $enrollment = CourseEnrollment::with(['user', 'course'])->findOrFail($id);

        $pdf = \PDF::loadView('admin.course_enrollments.pdf', compact('enrollment'));

        return $pdf->download('enrollment-' . $enrollment->id . '.pdf');
    }

    public function confirmPayment(Request $request, $id)
    {
        $enrollment = CourseEnrollment::findOrFail($id);
        // ... existing code ...
    }

    public function destroy($id)
    {
        try {
            $enrollment = CourseEnrollment::findOrFail($id);
            $enrollment->delete();
            Report::addToLog('حذف اشتراك دورة تدريبية');
            return response()->json(['id' => $id]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'حدث خطأ أثناء حذف الاشتراك'], 500);
        }
    }

    public function deleteAll(Request $request)
    {
        try {
            $ids = $request->ids;
            CourseEnrollment::whereIn('id', $ids)->delete();
            Report::addToLog('حذف متعدد لاشتراكات الدورات التدريبية');
            return response()->json(['message' => 'تم حذف الاشتراكات بنجاح']);
        } catch (\Exception $e) {
            return response()->json(['error' => 'حدث خطأ أثناء حذف الاشتراكات'], 500);
        }
    }

    /**
     * Verify bank transfer for course enrollment
     */
    public function verifyBankTransfer(Request $request, $id)
    {
        try {
            Log::info('Verify bank transfer request', [
                'enrollment_id' => $id,
                'has_file' => $request->hasFile('receipt_image'),
                'admin_id' => auth()->guard('admin')->id()
            ]);

            $enrollment = CourseEnrollment::findOrFail($id);

            // Check if enrollment is bank transfer and pending
            if ($enrollment->payment_method_id !== 5) {
                return response()->json([
                    'success' => false,
                    'message' => 'هذا الاشتراك ليس بتحويل بنكي'
                ], 400);
            }

            if ($enrollment->bank_transfer_status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'تم معالجة هذا التحويل مسبقاً'
                ], 400);
            }

            $admin = auth()->guard('admin')->user();
            if (!$admin) {
                return response()->json([
                    'success' => false,
                    'message' => 'غير مصرح لك بهذا الإجراء'
                ], 401);
            }

            // Handle receipt image upload if provided
            if ($request->hasFile('receipt_image')) {
                try {
                    $enrollment->addMediaFromRequest('receipt_image')
                        ->toMediaCollection('receipt_images');
                    Log::info('Receipt image uploaded successfully', ['enrollment_id' => $id]);
                } catch (\Exception $e) {
                    Log::error('Failed to upload receipt image', [
                        'enrollment_id' => $id,
                        'error' => $e->getMessage()
                    ]);
                    // Continue without failing the verification
                }
            }

            // Verify the bank transfer (without notes)
            $enrollment->verifyBankTransfer($admin->id, null);

            Report::addToLog('تأكيد تحويل بنكي لاشتراك دورة تدريبية');

            Log::info('Bank transfer verified successfully', ['enrollment_id' => $id]);

            return response()->json([
                'success' => true,
                'message' => 'تم تأكيد التحويل البنكي بنجاح'
            ]);

        } catch (\Exception $e) {
            Log::error('Error verifying bank transfer', [
                'enrollment_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تأكيد التحويل: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reject bank transfer for course enrollment
     */
    public function rejectBankTransfer($id)
    {
        try {
            $enrollment = CourseEnrollment::findOrFail($id);

            // Check if enrollment is bank transfer and pending
            if ($enrollment->payment_method_id !== 5) {
                return response()->json([
                    'success' => false,
                    'message' => 'هذا الاشتراك ليس بتحويل بنكي'
                ], 400);
            }

            if ($enrollment->bank_transfer_status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'تم معالجة هذا التحويل مسبقاً'
                ], 400);
            }

            $admin = auth()->guard('admin')->user();

            // Reject the bank transfer (without notes)
            $enrollment->rejectBankTransfer($admin->id, null);

            Report::addToLog('رفض تحويل بنكي لاشتراك دورة تدريبية');

            return response()->json([
                'success' => true,
                'message' => 'تم رفض التحويل البنكي'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء رفض التحويل: ' . $e->getMessage()
            ], 500);
        }
    }
}
