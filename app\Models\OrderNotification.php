<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class OrderNotification extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'type',
        'data',
        'sent_at',
    ];

    protected $casts = [
        'data' => 'array',
        'sent_at' => 'datetime',
    ];

    /**
     * Get the order that owns the notification
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }
}
