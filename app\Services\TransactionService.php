<?php

namespace App\Services;

use App\Models\Transaction;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Exception;

class TransactionService
{
    /**
     * Store a new transaction
     *
     * @param array $data
     * @return Transaction
     * @throws Exception
     */
    public function createTransaction(array $data): Transaction
    {
        try {
            DB::beginTransaction();
            // Generate transaction ID if not provided
            if (!isset($data['transaction_id'])) {
                $data['transaction_id'] = $this->generateTransactionId();
            }

            // Create the transaction
            $transaction = Transaction::create($data);

            DB::commit();

            return $transaction;

        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Generate unique transaction ID
     *
     * @return string
     */
    private function generateTransactionId(): string
    {
        do {
            $transactionId = 'TXN' . strtoupper(Str::random(8)) . time();
        } while (Transaction::where('transaction_id', $transactionId)->exists());

        return $transactionId;
    }
}