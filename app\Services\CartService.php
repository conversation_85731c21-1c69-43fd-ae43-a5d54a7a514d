<?php

namespace App\Services;

use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Product;
use App\Models\Service;
use App\Models\User;
use App\Models\Coupon;
use App\Models\Provider;
use App\Services\FeeCalculationService;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class CartService
{
    protected $feeCalculationService;

    public function __construct(FeeCalculationService $feeCalculationService)
    {
        $this->feeCalculationService = $feeCalculationService;
    }

    /**
     * Get the user's cart, create one if it doesn't exist
     *
     * @param User $user
     * @return Cart
     */
    public function getCart(User $user)
    {
        $cart = $user->cart;

        if (!$cart) {
            $cart = $user->cart()->create([
                'subtotal' => 0,
                'discount_amount' => 0,
                'booking_fee' => 0,
                'home_service_fee' => 0,
                'delivery_fee' => 0,
                'total' => 0,
                'loyalty_points_used' => 0,
            ]);
        }

        return $cart;
    }

    /**
     * Add an item (product or service) to the cart
     *
     * @param User $user
     * @param array $data
     * @return Cart|array
     * @throws \Exception
     */
    public function addToCart(User $user, array $data)
    {
        return DB::transaction(function () use ($user, $data) {
            $cart = $this->getCart($user);

            $itemType = $data['item_type'];
            $itemId = $data['item_id'];
            $quantity = $data['quantity'];
            $forceAdd = $data['force_add'] ?? false;

            // Check for conflicts before adding
            $conflictCheck = $this->checkCartConflicts($cart, $itemType, $itemId);

            if ($conflictCheck['has_conflict'] && !$forceAdd) {
                return [
                    'requires_confirmation' => true,
                    'conflict_type' => $conflictCheck['conflict_type'],
                    'message' => $conflictCheck['message'],
                    'items_to_remove' => $conflictCheck['items_to_remove'],
                ];
            }

            // If force_add is true, remove conflicting items first
            if ($forceAdd && $conflictCheck['has_conflict']) {
                $this->removeConflictingItems($cart, $conflictCheck['items_to_remove']);
            }

            if ($itemType === 'product') {
                return $this->addProductToCart($cart, $itemId, $quantity, $data);
            } elseif ($itemType === 'service') {
                return $this->addServiceToCart($cart, $itemId, $quantity, $data);
            }

            throw new \Exception('Invalid item type');
        });
    }

    /**
     * Check for cart conflicts when adding new items
     */
    private function checkCartConflicts(Cart $cart, $itemType, $itemId)
    {
        $hasConflict = false;
        $conflictType = '';
        $message = '';
        $itemsToRemove = [];

        if ($itemType === 'product') {
            $product = Product::findOrFail($itemId);
            $newProviderId = $product->provider_id;

            if ($cart->hasServices() && $cart->provider_id !== $newProviderId) {
                $hasConflict = true;
                $conflictType = 'product_different_provider_with_services'; // Adding product from different provider when cart has services
                $message = __('apis.product_different_provider_with_services');
                $itemsToRemove = $cart->items()
                    ->where('item_type', 'App\Models\Service')
                    ->pluck('id')
                    ->toArray();
            }
            elseif ($cart->hasProducts() && $cart->provider_id && $cart->provider_id !== $newProviderId) {
                $hasConflict = true;
                $conflictType = 'product_different_provider_with_products'; // Adding product from different provider when cart has products from single provider
                $message = __('apis.product_different_provider_with_products');
                $itemsToRemove = $cart->items()
                    ->where('item_type', 'App\Models\Product')
                    ->pluck('id')
                    ->toArray();
            }
        } elseif ($itemType === 'service') {
            $service = Service::findOrFail($itemId);
            $newProviderId = $service->provider_id;

            if ($cart->hasServices() && $cart->provider_id !== $newProviderId) {
                $hasConflict = true;
                $conflictType = 'service_different_provider'; // Case 2: Adding service from different provider when cart has services + products
                $message = 'Adding this service will remove all current services from your cart as they are from a different provider. Do you want to continue?';
                $itemsToRemove = $cart->items()
                    ->where('item_type', 'App\Models\Service')
                    ->pluck('id')
                    ->toArray();
            }
            elseif ($cart->hasProducts() && !$cart->provider_id) {
                $hasConflict = true;
                $conflictType = 'service_with_multiple_products';
                $message = 'Adding this service will remove all current products from your cart as services require a single provider. Do you want to continue?';
                $itemsToRemove = $cart->items()
                    ->where('item_type', 'App\Models\Product')
                    ->pluck('id')
                    ->toArray();
            }
            elseif ($cart->hasProducts() && $cart->provider_id && $cart->provider_id !== $newProviderId) {
                $hasConflict = true;
                $conflictType = 'service_different_provider_with_products';
                $message = __('apis.service_different_provider_with_products');
                $itemsToRemove = $cart->items()
                    ->where('item_type', 'App\Models\Product')
                    ->pluck('id')
                    ->toArray();
            }
        }

        return [
            'has_conflict' => $hasConflict,
            'conflict_type' => $conflictType,
            'message' => $message,
            'items_to_remove' => $itemsToRemove,
        ];
    }

    /**
     * Remove conflicting items from cart
     */
    private function removeConflictingItems(Cart $cart, array $itemIds)
    {
        if (!empty($itemIds)) {
            $cart->items()->whereIn('id', $itemIds)->delete();

            // Check if cart becomes empty after removing conflicting items
            if ($cart->items()->count() === 0) {
                $this->resetCartToEmptyState($cart);
            } else {
                // Reset cart provider if no services left
                if (!$cart->hasServices()) {
                    $cart->update([
                        'provider_id' => null,
                        'booking_fee' => 0,
                    ]);
                }
            }
        }
    }

    /**
     * Add a product to the cart
     */
    private function addProductToCart(Cart $cart, $productId, $quantity, $data)
    {
        $product = Product::findOrFail($productId);

        // Check if product is active
        if (!$product->is_active) {
            throw new \Exception(__('apis.product_not_available'));
        }

        // Check if product has enough stock
        if ($product->quantity < $quantity) {
            throw new \Exception(__('apis.insufficient_stock'));
        }

        // Check if product already exists in cart
        $existingCartItem = $cart->items()
            ->where('item_type', 'App\Models\Product')
            ->where('item_id', $productId)
            ->first();

        if ($existingCartItem) {
            // Update quantity
            $newQuantity = $existingCartItem->quantity + $quantity;

            // Check if new quantity exceeds stock
            if ($product->quantity < $newQuantity) {
                throw new \Exception(__('apis.insufficient_stock'));
            }

            $existingCartItem->update([
                'quantity' => $newQuantity,
                'total' => $product->price * $newQuantity,
            ]);
        } else {
            // Create new cart item
            $cart->items()->create([
                'item_type' => 'App\Models\Product',
                'item_id' => $productId,
                'quantity' => $quantity,
                'price' => $product->price,
                'total' => $product->price * $quantity,
                'options' => $data['options'] ?? null,
            ]);
        }

        // Update cart type and provider
        $cart->update(['type' => $cart->hasServices() ? 'mixed' : 'product' , 'provider_id' => $product->provider_id]);

        // Update fees using the fee calculation service
        $this->feeCalculationService->updateCartFees($cart, $data);
        $this->updateCartTotals($cart);

        return $cart->load(['items.item', 'provider']);
    }

    /**
     * Add a service to the cart
     */
    private function addServiceToCart(Cart $cart, $serviceId, $quantity, $data)
    {
        $service = Service::findOrFail($serviceId);

        // Check if service is active
        if (!$service->is_active) {
            throw new \Exception(__('apis.service_not_available'));
        }

        // Check provider availability
        if (!$this->isProviderAvailable($service->provider_id)) {
            throw new \Exception('Service provider is currently unavailable. Please try again later.');
        }

        // Check if service already exists in cart
        $existingCartItem = $cart->items()
            ->where('item_type', 'App\Models\Service')
            ->where('item_id', $serviceId)
            ->first();

        if ($existingCartItem) {
            throw new \Exception('Service already exists in cart');
        }

        // Create new cart item
        $options = $data['options'] ?? [];
        if (isset($data['booking_time'])) {
            $options['booking_time'] = $data['booking_time'];
        }

        $cart->items()->create([
            'item_type' => 'App\Models\Service',
            'item_id' => $serviceId,
            'quantity' => $quantity,
            'price' => $service->price,
            'total' => $service->price * $quantity,
            'options' => $options,
        ]);

        // Update cart provider and type
        $cart->update([
            'provider_id' => $service->provider_id,
            'type' => $cart->hasProducts() ? 'mixed' : 'service',
        ]);

        // Update fees using the fee calculation service
        $this->feeCalculationService->updateCartFees($cart, $data);
        $this->updateCartTotals($cart);

        return $cart->load(['items.item', 'provider']);
    }

    /**
     * Update a cart item
     *
     * @param User $user
     * @param array $data
     * @return Cart
     * @throws \Exception
     */
    public function updateCartItem(User $user, array $data)
    {
        return DB::transaction(function () use ($user, $data) {
            $cart = $this->getCart($user);
            $cartItem = $cart->items()
                ->where('item_type', 'App\\Models\\Product')
                ->where('item_id', $data['cart_item_id'])
                ->first();
            if (!$cartItem) {
                throw new \Exception(__('apis.not_found'));
            }

            if ($cartItem->isProduct()) {
                $product = $cartItem->item;

                // Check if product is active
                if (!$product->is_active) {
                    throw new \Exception(__('apis.product_not_available'));
                }

                // Check if product has enough stock
                if ($product->quantity < $data['quantity']) {
                    throw new \Exception(__('apis.insufficient_stock'));
                }

                // Update cart item
                $cartItem->update([
                    'quantity' => $data['quantity'],
                    'total' => $product->price * $data['quantity'],
                ]);
            } else {
                // Services cannot be updated in quantity
                throw new \Exception('Service quantity cannot be updated');
            }

            $this->updateCartTotals($cart);
            return $cart->load(['items.item', 'provider']);
        });
    }

    /**
     * Remove an item from the cart
     *
     * @param User $user
     * @param int $cartItemId
     * @return Cart
     * @throws \Exception
     */
    public function removeFromCart(User $user, int $cartItemId)
    {
        return DB::transaction(function () use ($user, $cartItemId) {
            $cart = $this->getCart($user);
            $cartItem = $cart->items()->findOrFail($cartItemId);

            $cartItem->delete();

            // If cart becomes empty, reset everything including coupon and loyalty points
            if ($cart->items()->count() === 0) {
                $this->resetCartToEmptyState($cart);
            } else {
                // If cart still has items, just update totals
                $this->updateCartTotals($cart);
            }

            return $cart->load(['items.item', 'provider']);
        });
    }

    /**
     * Clear the cart
     *
     * @param User $user
     * @return Cart
     */
    public function clearCart(User $user)
    {
        return DB::transaction(function () use ($user) {
            $cart = $this->getCart($user);

            // Delete all cart items
            $cart->items()->delete();

            // Reset cart completely - remove coupon, loyalty points, and all calculations
            $this->resetCartToEmptyState($cart);

            return $cart->fresh(); // Return fresh instance to ensure all changes are reflected
        });
    }

    /**
     * Apply coupon to cart
     *
     * @param User $user
     * @param string $couponCode
     * @return Cart
     * @throws \Exception
     */
    public function applyCoupon(User $user, string $couponCode)
    {
        return DB::transaction(function () use ($user, $couponCode) {
            $cart = $this->getCart($user);

            if ($cart->items()->count() === 0) {
                throw new \Exception('Cart is empty');
            }

            $coupon = Coupon::where('coupon_num', $couponCode)
                ->with(['services', 'provider'])
                ->first();

            if (!$coupon) {
                throw new \Exception('Invalid coupon code');
            }

            // Check coupon validity (includes usage limit check)
            if (!$this->isCouponValid($coupon)) {
                throw new \Exception('Coupon is not valid or has reached usage limit');
            }

            // Check provider constraint
            if ($coupon->provider_id && $cart->provider_id !== $coupon->provider_id) {
                throw new \Exception('This coupon is only valid for ' . $coupon->provider->commercial_name);
            }

            // Check service constraints
            if (!$this->isCouponValidForCartServices($coupon, $cart)) {
                throw new \Exception('This coupon is not applicable to the services in your cart');
            }

            // Check minimum order value
            $cartSubtotal = $this->calculateCartSubtotal($cart);
            if (!$coupon->meetsMinimumOrderValue($cartSubtotal)) {
                throw new \Exception('Minimum order value of ' . number_format((float)$coupon->minimum_order_value, 2) . ' SAR required to use this coupon');
            }

            // Check if cart already has a coupon applied
            if ($cart->coupon_code) {
                // If it's the same coupon, no need to reapply
                if ($cart->coupon_code === $couponCode) {
                    throw new \Exception('Coupon already applied');
                }

                // If it's a different coupon, remove the old one first
                $this->removeCouponFromCart($cart);
            }

            // Calculate discount
            $discountAmount = $this->calculateCouponDiscount($cart, $coupon);

            // Update cart with new coupon
            $cart->update([
                'coupon_code' => $couponCode,
                'discount_amount' => $discountAmount,
            ]);

            $this->updateCartTotals($cart);
            return $cart->load(['items.item', 'provider', 'coupon']);
        });
    }

    /**
     * Remove coupon from cart
     *
     * @param User $user
     * @return Cart
     * @throws \Exception
     */
    public function removeCoupon(User $user)
    {
        return DB::transaction(function () use ($user) {
            $cart = $this->getCart($user);

            // Check if cart has a coupon applied
            if (!$cart->coupon_code) {
                throw new \Exception('No coupon applied to cart');
            }

            // Remove coupon from cart
            $this->removeCouponFromCart($cart);

            $this->updateCartTotals($cart);
            return $cart->load(['items.item', 'provider']);
        });
    }

    /**
     * Remove coupon from cart (helper method)
     *
     * @param Cart $cart
     * @return void
     */
    private function removeCouponFromCart(Cart $cart)
    {
        // Calculate the total before removing the coupon (add back the discount)
        $totalBeforeCoupon = $cart->total + $cart->discount_amount;

        $cart->update([
            'coupon_code' => null,
            'discount_amount' => 0,
            'total' => $totalBeforeCoupon,
        ]);
    }

    /**
     * Apply loyalty points to cart
     *
     * @param User $user
     * @param int $points
     * @return Cart
     * @throws \Exception
     */
    public function applyLoyaltyPoints(User $user, int $points)
    {
        return DB::transaction(function () use ($user, $points) {
            $cart = $this->getCart($user);

            if ($cart->items()->count() === 0) {
                throw new \Exception('Cart is empty');
            }

            // Get loyalty points settings
            $appInfo = \App\Models\SiteSetting::pluck('value', 'key')->toArray();
            $settings = SettingService::appInformations($appInfo);

            // Check if loyalty points are enabled
            if (!$settings['loyalty_points_enabled']) {
                throw new \Exception('Loyalty points system is disabled');
            }

            // Check minimum points requirement
            $minRedeem = $settings['loyalty_points_min_redeem'] ?? 10;
            if ($points < $minRedeem) {
                throw new \Exception("Minimum {$minRedeem} points required to redeem");
            }

            // Check if user has enough points
            if ($user->loyalty_points < $points) {
                throw new \Exception('Insufficient loyalty points');
            }

            // Calculate maximum points that can be used (based on percentage)
            $maxPercentage = $settings['loyalty_points_max_redeem_percentage'] ?? 50;
            $cartTotal = $cart->subtotal - $cart->discount_amount + $cart->booking_fee;
            $maxPointsValue = ($cartTotal * $maxPercentage) / 100;

            // Convert points to value
            $redeemRate = $settings['loyalty_points_redeem_rate'] ?? 1;
            $pointsValue = $points * $redeemRate;

            if ($pointsValue > $maxPointsValue) {
                $maxPoints = floor($maxPointsValue / $redeemRate);
                throw new \Exception("Cannot use more than {$maxPoints} points ({$maxPercentage}% of cart total)");
            }

            $cart->update(['loyalty_points_used' => $points]);
            $this->updateCartTotals($cart);

            return $cart->load(['items.item', 'provider']);
        });
    }

    /**
     * Check if provider is available
     */
    private function isProviderAvailable($providerId)
    {
        $provider = Provider::find($providerId);
        return $provider && $provider->accept_orders == 1 && $provider->status === 'accepted';
    }

    /**
     * Check if coupon is valid
     */
    private function isCouponValid(Coupon $coupon)
    {
        // Check if coupon is active
        if (!$coupon->is_active) {
            return false;
        }

        // Check dates
        $now = Carbon::now();
        if ($coupon->start_date && $now->lt($coupon->start_date)) {
            return false;
        }
        if ($coupon->expire_date && $now->gt($coupon->expire_date)) {
            return false;
        }

        // Check usage limit
        if (!$coupon->hasUsagesRemaining()) {
            return false;
        }

        return true;
    }

    /**
     * Check if coupon is valid for the services in the cart
     */
    private function isCouponValidForCartServices(Coupon $coupon, Cart $cart): bool
    {
        // If coupon has no service restrictions, it's valid for all services
        if ($coupon->services()->count() === 0) {
            return true;
        }

        // Get service IDs from cart items
        $cartServiceIds = $cart->items()
            ->where('item_type', 'App\Models\Service')
            ->pluck('item_id')
            ->toArray();

        // If cart has no services, coupon is not applicable
        if (empty($cartServiceIds)) {
            return false;
        }

        // Check if any cart service is linked to this coupon
        return $coupon->canBeAppliedToServices($cartServiceIds);
    }

    /**
     * Calculate cart subtotal before any discounts
     */
    private function calculateCartSubtotal(Cart $cart): float
    {
        $subtotal = 0;
        foreach ($cart->items as $item) {
            $subtotal += $item->quantity * $item->price;
        }
        return $subtotal;
    }

    /**
     * Reset cart to empty state - removes coupon, loyalty points, and all calculations
     */
    private function resetCartToEmptyState(Cart $cart): void
    {
        $cart->update([
            'provider_id' => null,
            'type' => 'product',
            'subtotal' => 0,
            'discount_amount' => 0,
            'coupon_code' => null,
            'booking_fee' => 0,
            'delivery_fee' => 0,
            'total' => 0,
            'loyalty_points_used' => 0,
            'loyalty_points_discount' => 0,
        ]);
    }

    /**
     * Calculate coupon discount
     */
    private function calculateCouponDiscount(Cart $cart, Coupon $coupon)
    {
        $subtotal = $cart->subtotal;

        if ($coupon->type === 'ratio') {
            $discount = $subtotal * ($coupon->discount / 100);
            if ($coupon->max_discount && $discount > $coupon->max_discount) {
                $discount = $coupon->max_discount;
            }
        } else {
            $discount = min($coupon->discount, $subtotal);
        }

        return $discount;
    }

    /**
     * Update cart totals
     *
     * @param Cart $cart
     * @return void
     */
    private function updateCartTotals(Cart $cart)
    {
        $cart->refresh();

        // Calculate subtotal
        $subtotal = $cart->items()->sum('total');

        // Calculate final total including all fees
        $total = $subtotal + $cart->booking_fee + $cart->home_service_fee + $cart->delivery_fee - $cart->discount_amount - $cart->loyalty_points_used;
        $total = max(0, $total); // Ensure total is not negative

        $cart->update([
            'subtotal' => $subtotal,
            'total' => $total,
        ]);
    }

    public function clearCartServices(User $user)
    {
        return DB::transaction(function () use ($user) {
            $cart = $this->getCart($user);

            // Delete only service items
            $cart->items()->where('item_type', 'App\\Models\\Service')->delete();

            // Optionally, recalculate cart totals after removing services
            $this->updateCartTotals($cart);

            return $cart;
        });
    }
}
