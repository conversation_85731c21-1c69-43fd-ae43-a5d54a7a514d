<?php

namespace Database\Seeders;

use App\Models\PaymentMethod;
use Illuminate\Database\Seeder;

class PaymentMethodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $paymentMethods = [
            [
                'id' => 1,
                'name' => [
                    'en' => 'Visa',
                    'ar' => 'فيزا'
                ],
                'image' => $this->getImageFilename('visa.png'),
                'is_active' => true
            ],
            [
                'id' => 2,
                'name' => [
                    'en' => 'Mada',
                    'ar' => 'مدى'
                ],
                'image' => $this->getImageFilename('mada.png'),
                'is_active' => true
            ],
            [
                'id' => 3,
                'name' => [
                    'en' => 'Apple Pay',
                    'ar' => 'آبل باي'
                ],
                'image' => $this->getImageFilename('applepay.png'),
                'is_active' => true
            ],
            [
                'id' => 4,
                'name' => [
                    'en' => 'Google Pay',
                    'ar' => 'جوجل باي'
                ],
                'image' => $this->getImageFilename('googlepay.png'),
                'is_active' => true
            ],
            [
                'id' => 5,
                'name' => [
                    'en' => 'Bank Transfer',
                    'ar' => 'تحويل بنكي'
                ],
                'image' => $this->getImageFilename('bank-transfer.png'),
                'is_active' => true
            ],
            [
                'id' => 6,
                'name' => [
                    'en' => 'Wallet',
                    'ar' => 'المحفظه'
                ],
                'image' => $this->getImageFilename('wallet.png'),
                'is_active' => true
            ],
        ];

        foreach ($paymentMethods as $method) {
            PaymentMethod::updateOrCreate(
                ['id' => $method['id']], // Search criteria
                $method // Data to update or create
            );
        }
    }

    /**
     * Get the correct image filename for payment method
     * The BaseModel's getImageAttribute will handle the full path construction
     */
    private function getImageFilename($filename)
    {
        // Check if the image exists in the paymentmethods directory
        $imagePath = public_path("storage/images/paymentmethods/{$filename}");

        if (file_exists($imagePath)) {
            // Return just the filename - BaseModel will construct the full URL
            return $filename;
        }

        // If image doesn't exist, return default.png
        // The UploadTrait will handle showing the default image
        return 'default.png';
    }
}
