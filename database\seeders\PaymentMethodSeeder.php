<?php

namespace Database\Seeders;

use App\Models\PaymentMethod;
use Illuminate\Database\Seeder;

class PaymentMethodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $paymentMethods = [
            [
                'name' => [
                    'en' => 'Visa',
                    'ar' => 'فيزا'
                ],
                'image' => 'storage/images/paymentmethods/visa.png',
                'is_active' => true
            ],
            [
                'name' => [
                    'en' => 'Mada',
                    'ar' => 'مدى'
                ],
                'image' => 'storage/images/paymentmethods/mada.png',
                'is_active' => true
            ],
            [
                'name' => [
                    'en' => 'Apple Pay',
                    'ar' => 'آبل باي'
                ],
                'image' => 'storage/images/paymentmethods/applepay.png',
                'is_active' => true
            ],
            [
                'name' => [
                    'en' => 'Google Pay',
                    'ar' => 'جوجل باي'
                ],
                'image' => 'storage/images/paymentmethods/googlepay.png',
                'is_active' => true
            ],
            [
                'name' => [
                    'en' => 'banke Transfer',
                    'ar' => 'تحويل بنكي'
                ],
                
                'is_active' => true
            ],
            [
                'name' => [
                    'en' => 'Wallet',
                    'ar' => 'المحفظه'
                ],
                
                'is_active' => true
            ],
         
        ];

        foreach ($paymentMethods as $method) {
            PaymentMethod::create($method);
        }
    }
}
