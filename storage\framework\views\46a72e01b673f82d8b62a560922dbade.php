

<?php $__env->startSection('css'); ?>
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css')); ?>">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<section id="multiple-column-form">
    <div class="row match-height">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title"><?php echo e(__('admin.account_deletion_request_details')); ?></h4>
                    <div class="card-header-toolbar d-flex align-items-center">
                        <?php switch($request->status):
                            case ('pending'): ?>
                                <span class="badge badge-warning mr-2"><?php echo e(__('admin.pending')); ?></span>
                                <?php break; ?>
                            <?php case ('approved'): ?>
                                <span class="badge badge-success mr-2"><?php echo e(__('admin.approved')); ?></span>
                                <?php break; ?>
                            <?php case ('rejected'): ?>
                                <span class="badge badge-danger mr-2"><?php echo e(__('admin.rejected')); ?></span>
                                <?php break; ?>
                        <?php endswitch; ?>

                        <a href="<?php echo e(route('admin.account-deletion-requests.index')); ?>" class="btn btn-primary btn-sm mr-1">
                            <i class="feather icon-arrow-left"></i> <?php echo e(__('admin.back')); ?>

                        </a>

                        <?php if($request->isPending()): ?>
                            <button class="btn btn-success btn-sm mr-1 approve-request" data-id="<?php echo e($request->id); ?>">
                                <i class="feather icon-check"></i> <?php echo e(__('admin.accept')); ?>

                            </button>
                            <button class="btn btn-danger btn-sm reject-request" data-id="<?php echo e($request->id); ?>">
                                <i class="feather icon-x"></i> <?php echo e(__('admin.reject')); ?>

                            </button>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="card-content">
                    <div class="card-body">
                        <div class="row">
                            <!-- User Information -->
                            <div class="col-md-6">
                                <h5><?php echo e(__('admin.user_information')); ?></h5>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong><?php echo e(__('admin.name')); ?>:</strong></td>
                                        <td><?php echo e($request->user->name ?? __('admin.deleted_user')); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong><?php echo e(__('admin.email')); ?>:</strong></td>
                                        <td><?php echo e($request->user->email ?? __('admin.not_available')); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong><?php echo e(__('admin.phone')); ?>:</strong></td>
                                        <td><?php echo e($request->user->full_phone ?? __('admin.not_available')); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong><?php echo e(__('admin.user_type')); ?>:</strong></td>
                                        <td>
                                            <?php if($request->user): ?>
                                                <?php switch($request->user->type):
                                                    case ('client'): ?>
                                                        <span class="badge badge-info"><?php echo e(__('admin.client')); ?></span>
                                                        <?php break; ?>
                                                    <?php case ('provider'): ?>
                                                        <span class="badge badge-warning"><?php echo e(__('admin.provider')); ?></span>
                                                        <?php break; ?>
                                                    <?php case ('delivery'): ?>
                                                        <span class="badge badge-secondary"><?php echo e(__('admin.delivery')); ?></span>
                                                        <?php break; ?>
                                                    <?php default: ?>
                                                        <span class="badge badge-light"><?php echo e($request->user->type); ?></span>
                                                <?php endswitch; ?>
                                            <?php else: ?>
                                                <span class="text-muted"><?php echo e(__('admin.not_available')); ?></span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong><?php echo e(__('admin.registration_date')); ?>:</strong></td>
                                        <td><?php echo e($request->user->created_at->format('Y-m-d H:i') ?? __('admin.not_available')); ?></td>
                                    </tr>
                                </table>
                            </div>

                            <!-- Request Information -->
                            <div class="col-md-6">
                                <h5><?php echo e(__('admin.request_information')); ?></h5>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong><?php echo e(__('admin.request_date')); ?>:</strong></td>
                                        <td><?php echo e($request->created_at->format('Y-m-d H:i')); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong><?php echo e(__('admin.status')); ?>:</strong></td>
                                        <td>
                                            <?php switch($request->status):
                                                case ('pending'): ?>
                                                    <span class="badge badge-warning"><?php echo e(__('admin.pending')); ?></span>
                                                    <?php break; ?>
                                                <?php case ('approved'): ?>
                                                    <span class="badge badge-success"><?php echo e(__('admin.approved')); ?></span>
                                                    <?php break; ?>
                                                <?php case ('rejected'): ?>
                                                    <span class="badge badge-danger"><?php echo e(__('admin.rejected')); ?></span>
                                                    <?php break; ?>
                                            <?php endswitch; ?>
                                        </td>
                                    </tr>
                                    <?php if($request->processed_at): ?>
                                        <tr>
                                            <td><strong><?php echo e(__('admin.processed_date')); ?>:</strong></td>
                                            <td><?php echo e($request->processed_at->format('Y-m-d H:i')); ?></td>
                                        </tr>
                                    <?php endif; ?>
                                    <?php if($request->processedBy): ?>
                                        <tr>
                                            <td><strong><?php echo e(__('admin.processed_by')); ?>:</strong></td>
                                            <td><?php echo e($request->processedBy->name); ?></td>
                                        </tr>
                                    <?php endif; ?>
                                </table>
                            </div>
                        </div>

                        <!-- User Reason -->
                        <?php if($request->reason): ?>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h5><?php echo e(__('admin.user_reason')); ?></h5>
                                    <div class="card">
                                        <div class="card-body">
                                            <p class="card-text"><?php echo e($request->reason); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Admin Notes -->
                        <?php if($request->admin_notes): ?>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h5><?php echo e(__('admin.admin_notes')); ?></h5>
                                    <div class="card">
                                        <div class="card-body">
                                            <p class="card-text"><?php echo e($request->admin_notes); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Approve Modal -->
<div class="modal fade" id="approveModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo e(__('admin.approve_deletion_request')); ?></h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="approveForm">
                    <div class="form-group">
                        <label><?php echo e(__('admin.admin_notes')); ?></label>
                        <textarea class="form-control" name="admin_notes" rows="3" placeholder="<?php echo e(__('admin.enter_admin_notes')); ?>"></textarea>
                    </div>
                </form>
                <div class="alert alert-warning">
                    <i class="feather icon-alert-triangle"></i>
                    <?php echo e(__('admin.approve_deletion_warning')); ?>

                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(__('admin.cancel')); ?></button>
                <button type="button" class="btn btn-success" id="confirmApprove"><?php echo e(__('admin.accept')); ?></button>
            </div>
        </div>
    </div>
</div>

<!-- Reject Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo e(__('admin.reject_deletion_request')); ?></h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="rejectForm">
                    <div class="form-group">
                        <label><?php echo e(__('admin.rejection_reason')); ?> <span class="text-danger">*</span></label>
                        <textarea class="form-control" name="admin_notes" rows="3" placeholder="<?php echo e(__('admin.enter_rejection_reason')); ?>" required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(__('admin.cancel')); ?></button>
                <button type="button" class="btn btn-danger" id="confirmReject"><?php echo e(__('admin.reject')); ?></button>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
    <script src="<?php echo e(asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js')); ?>"></script>
    <script>
        let currentRequestId = <?php echo e($request->id); ?>;

        $(document).ready(function(){
            // Handle approve button click
            $(document).on('click', '.approve-request', function(e){
                e.preventDefault();
                $('#approveModal').modal('show');
            });

            // Handle reject button click
            $(document).on('click', '.reject-request', function(e){
                e.preventDefault();
                $('#rejectModal').modal('show');
            });

            // Handle approve confirmation
            $('#confirmApprove').click(function(){
                let adminNotes = $('#approveForm textarea[name="admin_notes"]').val();

                $.ajax({
                    url: '<?php echo e(url('admin/account-deletion-requests')); ?>/' + currentRequestId + '/approve',
                    method: 'POST',
                    data: {
                        _token: '<?php echo e(csrf_token()); ?>',
                        admin_notes: adminNotes
                    },
                    success: function(response) {
                        $('#approveModal').modal('hide');
                        Swal.fire({
                            title: '<?php echo e(__('admin.success')); ?>',
                            text: response.message,
                            type: 'success',
                            confirmButtonText: '<?php echo e(__('admin.close')); ?>'
                        });
                        setTimeout(function(){
                            window.location.reload();
                        }, 1000);
                    },
                    error: function(xhr) {
                        $('#approveModal').modal('hide');
                        let errorMessage = xhr.responseJSON?.error || xhr.responseJSON?.message || '<?php echo e(__('admin.error_occurred')); ?>';
                        Swal.fire({
                            title: '<?php echo e(__('admin.error')); ?>',
                            text: errorMessage,
                            type: 'error',
                            confirmButtonText: '<?php echo e(__('admin.close')); ?>'
                        });
                    }
                });
            });

            // Handle reject confirmation
            $('#confirmReject').click(function(){
                let adminNotes = $('#rejectForm textarea[name="admin_notes"]').val();

                if(!adminNotes.trim()) {
                    alert('<?php echo e(__('admin.rejection_reason_required')); ?>');
                    return;
                }

                $.ajax({
                    url: '<?php echo e(url('admin/account-deletion-requests')); ?>/' + currentRequestId + '/reject',
                    method: 'POST',
                    data: {
                        _token: '<?php echo e(csrf_token()); ?>',
                        admin_notes: adminNotes
                    },
                    success: function(response) {
                        $('#rejectModal').modal('hide');
                        Swal.fire({
                            title: '<?php echo e(__('admin.success')); ?>',
                            text: response.message,
                            type: 'success',
                            confirmButtonText: '<?php echo e(__('admin.close')); ?>'
                        });
                        setTimeout(function(){
                            window.location.reload();
                        }, 1000);
                    },
                    error: function(xhr) {
                        $('#rejectModal').modal('hide');
                        let errorMessage = xhr.responseJSON?.message || '<?php echo e(__('admin.error_occurred')); ?>';
                        Swal.fire({
                            title: '<?php echo e(__('admin.error')); ?>',
                            text: errorMessage,
                            type: 'error',
                            confirmButtonText: '<?php echo e(__('admin.close')); ?>'
                        });
                    }
                });
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layout.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Workstation\Taswk\sorriso-backend\resources\views/admin/account-deletion-requests/show.blade.php ENDPATH**/ ?>