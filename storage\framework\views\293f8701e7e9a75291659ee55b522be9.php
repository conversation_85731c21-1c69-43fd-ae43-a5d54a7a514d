<div class="position-relative" style="overflow: auto">
    
    
    
    
    <table class="table " id="tab">
        <thead>
            <tr>
                <th>
                    <label class="container-checkbox">
                      <input type="checkbox" value="value1" name="name1" id="checkedAll">
                      <span class="checkmark"></span>
                    </label>
                  </th>
                  <th><?php echo e(__('admin.date')); ?></th>
                  <th><?php echo e(__('admin.user')); ?></th>
                  <th><?php echo e(__('admin.user_type')); ?></th>
                  <th><?php echo e(__('admin.email')); ?></th>
                  <th><?php echo e(__('admin.phone')); ?></th>
                  <th><?php echo e(__('admin.status')); ?></th>
                  <th><?php echo e(__('admin.control')); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $account_deletion_requests; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr class="delete_row">
                <td class="text-center">
                    <label class="container-checkbox">
                        <input type="checkbox" class="checkSingle" id="<?php echo e($row->id); ?>">
                        <span class="checkmark"></span>
                    </label>
                </td>
                <td><?php echo e($row->created_at->format('d/m/Y')); ?></td>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="avatar avatar-sm mr-2">
                            <?php if($row->user && $row->user->getFirstMediaUrl('profile')): ?>
                                <img src="<?php echo e($row->user->getFirstMediaUrl('profile')); ?>" alt="<?php echo e($row->user->name); ?>" class="round" width="30px" height="30px">
                            <?php else: ?>
                                <div class="avatar-content bg-light-primary">
                                    <i class="feather icon-user"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div>
                            <h6 class="mb-0"><?php echo e($row->user->name ?? __('admin.deleted_user')); ?></h6>
                            <small class="text-muted">ID: <?php echo e($row->user_id); ?></small>
                        </div>
                    </div>
                </td>
                <td>
                    <?php if($row->user): ?>
                        <?php switch($row->user->type):
                            case ('client'): ?>
                                <span class="badge badge-info"><?php echo e(__('admin.client')); ?></span>
                                <?php break; ?>
                            <?php case ('provider'): ?>
                                <span class="badge badge-warning"><?php echo e(__('admin.provider')); ?></span>
                                <?php break; ?>
                            <?php case ('delivery'): ?>
                                <span class="badge badge-secondary"><?php echo e(__('admin.delivery')); ?></span>
                                <?php break; ?>
                            <?php default: ?>
                                <span class="badge badge-light"><?php echo e($row->user->type); ?></span>
                        <?php endswitch; ?>
                    <?php else: ?>
                        <span class="text-muted">-</span>
                    <?php endif; ?>
                </td>
                <td><?php echo e($row->user->email ?? __('admin.not_set')); ?></td>
                <td><?php echo e($row->user->full_phone ?? __('admin.not_set')); ?></td>
                
                <td>
                    <?php switch($row->status):
                        case ('pending'): ?>
                            <span class="badge badge-warning"><?php echo e(__('admin.pending')); ?></span>
                            <?php break; ?>
                        <?php case ('approved'): ?>
                            <span class="badge badge-success"><?php echo e(__('admin.approved')); ?></span>
                            <?php break; ?>
                        <?php case ('rejected'): ?>
                            <span class="badge badge-danger"><?php echo e(__('admin.rejected')); ?></span>
                            <?php break; ?>
                        <?php default: ?>
                            <span class="badge badge-secondary"><?php echo e($row->status); ?></span>
                    <?php endswitch; ?>
                </td>
              
                <td class="product-action">
                    <span class="d-none d-md-inline">
                        <a href="<?php echo e(route('admin.account-deletion-requests.show', $row->id)); ?>" class="btn btn-warning btn-sm p-1">
                            <i class="feather icon-eye"></i>
                        </a>
                    </span>

                    <?php if($row->isPending()): ?>
                        <span class="d-none d-md-inline">
                            <button class="btn btn-success btn-sm p-1 approve-request" data-id="<?php echo e($row->id); ?>">
                                <i class="feather icon-check"></i>
                            </button>
                        </span>
                        <span class="d-none d-md-inline">
                            <button class="btn btn-danger btn-sm p-1 reject-request" data-id="<?php echo e($row->id); ?>">
                                <i class="feather icon-x"></i>
                            </button>
                        </span>
                    <?php else: ?>
                        <span class="text-muted d-none d-md-inline">
                            <?php if($row->isApproved()): ?>
                                <span class="btn btn-sm btn-outline-success p-1">
                                    <i class="feather icon-check-circle"></i>
                                </span>
                            <?php else: ?>
                                <span class="btn btn-sm btn-outline-danger p-1">
                                    <i class="feather icon-x-circle"></i>
                                </span>
                            <?php endif; ?>
                        </span>
                    <?php endif; ?>

                    <span class="actions-dropdown d-md-none">
                        <div class="dropdown">
                            <button class="btn btn-sm btn-secondary dropdown-toggle" type="button" id="actions-menu-<?php echo e($row->id); ?>" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <?php echo e(__('admin.actions')); ?>

                            </button>
                            <div class="dropdown-menu" aria-labelledby="actions-menu-<?php echo e($row->id); ?>">
                                <a class="dropdown-item" href="<?php echo e(route('admin.account-deletion-requests.show', $row->id)); ?>"><?php echo e(__('admin.show')); ?></a>
                                <?php if($row->isPending()): ?>
                                    <button class="dropdown-item approve-request" data-id="<?php echo e($row->id); ?>"><?php echo e(__('admin.approve')); ?></button>
                                    <button class="dropdown-item reject-request" data-id="<?php echo e($row->id); ?>"><?php echo e(__('admin.reject')); ?></button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </span>
                </td>
            </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>
    
    
    <?php if($account_deletion_requests->count() == 0): ?>
        <div class="d-flex flex-column w-100 align-center mt-4">
            <img src="<?php echo e(asset('admin/app-assets/images/pages/404.png')); ?>" alt="">
            <span class="mt-2" style="font-family: cairo"><?php echo e(__('admin.there_are_no_matches_matching')); ?></span>
        </div>
    <?php endif; ?>
    

</div>

<?php if($account_deletion_requests->count() > 0 && $account_deletion_requests instanceof \Illuminate\Pagination\AbstractPaginator ): ?>
    <div class="d-flex justify-content-center mt-3">
        <?php echo e($account_deletion_requests->links()); ?>

    </div>
<?php endif; ?>

<?php /**PATH D:\Workstation\Taswk\sorriso-backend\resources\views/admin/account-deletion-requests/table.blade.php ENDPATH**/ ?>