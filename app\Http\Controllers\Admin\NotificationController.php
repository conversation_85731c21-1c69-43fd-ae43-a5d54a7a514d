<?php

namespace App\Http\Controllers\Admin;

use App\Jobs\Notify;
use App\Models\User;
use App\Jobs\SendSms;
use App\Models\Admin;
use App\Jobs\AdminNotify;
use App\Jobs\SendEmailJob;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\SendNotificationRequest;

class NotificationController extends Controller
{
    public function index()
    {
       return view('admin.notifications.index');
    }


    public function sendNotifications(SendNotificationRequest $request)
    {
        // Get users based on user_type
        switch ($request->user_type) {
            case 'all':
                $rows = User::get();
                break;
            case 'clients':
                $rows = User::where('type', 'client')->get();
                break;
            case 'provider':
                $rows = User::where('type', 'provider')->get();
                break;
            case 'admins':
                $rows = Admin::get();
                break;
            default:
                $rows = collect(); // Empty collection as fallback
        }

        // Send notifications
        if ($request->user_type === 'admins') {
            dispatch(new AdminNotify($rows, $request));
        } else {
            dispatch(new Notify($rows, $request));
        }

        return response()->json(['success' => true]);
    }
}
