<div class="tab-pane fade active show" id="data">
    <div class="card">
        <div class="card-header">
            <h5 class="card-title"><?php echo e(__('admin.data_user')); ?></h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-12">
                    <div class="imgMontg col-12 text-center">
                        <div class="dropBox">
                            <div class="textCenter">
                                <div class="imagesUploadBlock">
                                    <label class="uploadImg">
                                        <span><?php echo e(__('admin.image')); ?></span>
                                        <span><i class="feather icon-image"></i></span>
                                        <input type="file" accept="image/*" name="image" class="imageUploader">
                                    </label>
                                    <div class="uploadedBlock">
                                        <img src="<?php echo e($row->image); ?>">
                                        <button class="close"><i class="la la-times"></i></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

    <div class="col-md-6 col-12">
        <div class="form-group">
            <label for="first-name-column"><?php echo e(__('admin.name')); ?></label>
            <div class="controls">
                <input type="text" name="name" value="<?php echo e($row->name); ?>" class="form-control" placeholder="<?php echo e(__('admin.write_the_name')); ?>" required data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>" disabled>
            </div>
        </div>
    </div>
    <div class="col-md-6 col-12">
        <div class="form-group">
            <label for="first-name-column"><?php echo e(__('admin.phone_number')); ?></label>
            <div class="row">
                <div class="col-md-4 col-12">
                    <select name="country_code" class="form-control select2" disabled>
                        <?php $__currentLoopData = $countries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($country->key); ?>"
                                <?php if($row->country_code == $country->key): ?>
                                    selected
                                <?php endif; ?> >
                            <?php echo e('+'.$row->country_code); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div class="col-md-8 col-12">
                    <div class="controls">
                        <input type="number" name="phone" value="<?php echo e($row->phone); ?>"  class="form-control" placeholder="<?php echo e(__('admin.enter_phone_number')); ?>" required data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>" data-validation-number-message="<?php echo e(__('admin.the_phone_number_ must_not_have_charachters_or_symbol')); ?>"  disabled>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6 col-12">
        <div class="form-group">
            <label for="first-name-column"><?php echo e(__('admin.email')); ?></label>
            <div class="controls">
                <input type="email" name="email" value="<?php echo e($row->email); ?>" class="form-control" placeholder="<?php echo e(__('admin.enter_the_email')); ?>" required data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>" data-validation-email-message="<?php echo e(__('admin.email_formula_is_incorrect')); ?>" disabled>
            </div>
        </div>
    </div>


    <div class="col-md-6 col-12">
        <div class="form-group">
            <label><?php echo e(__('admin.Region')); ?></label>
            <input type="text" class="form-control"
                   value="<?php echo e(optional($row->region)->name); ?>"
                   disabled>
        </div>
    </div>

    <div class="col-md-6 col-12">
        <div class="form-group">
            <label><?php echo e(__('admin.City')); ?></label>
            <input type="text" class="form-control"
                   value="<?php echo e(optional($row->city)->name); ?>"
                   disabled>
        </div>
    </div>







    <div class="col-md-6 col-12">
        <div class="form-group">
            <label for="first-name-column"><?php echo e(__('admin.Validity')); ?></label>
            <div class="controls">
                <input type="text" name="status" value="<?php echo e($row->status == 'blocked' ? __('admin.Prohibited') : __('admin.Unspoken')); ?>" class="form-control" disabled >
            </div>
        </div>
    </div>

    <div class="col-md-6 col-12">
        <div class="form-group">
            <label for="first-name-column"><?php echo e(__('admin.activation')); ?></label>
            <div class="controls">
                <input type="text" name="active" value="<?php echo e($row->is_active == 1 ? __('admin.activate') : __('admin.dis_activate')); ?>" class="form-control" disabled >
            </div>
        </div>
    </div>


                <div class="col-12 d-flex justify-content-center mt-3">
                    <a href="<?php echo e(url()->previous()); ?>" type="reset" class="btn btn-outline-warning mr-1 mb-1"><?php echo e(__('admin.back')); ?></a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH D:\Workstation\Taswk\sorriso-backend\resources\views/admin/clients/tabs/data.blade.php ENDPATH**/ ?>