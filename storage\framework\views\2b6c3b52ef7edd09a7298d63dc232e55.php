

<?php $__env->startSection('css'); ?>
<!-- SweetAlert2 CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
<style>
/* Header Styles - Fix white text on white background */
.card-header.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: #ffffff !important;
    min-height: 100px;
}

.card-header.bg-gradient-primary * {
    color: #ffffff !important;
}

.card-header.bg-gradient-primary .card-title {
    color: #ffffff !important;
    font-weight: 600;
    font-size: 1.25rem;
}

.card-header.bg-gradient-primary small {
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 0.8rem;
}

.card-header.bg-gradient-primary .text-white {
    color: #ffffff !important;
}

.card-header.bg-gradient-primary .text-white-50 {
    color: rgba(255, 255, 255, 0.7) !important;
}

.card-header.bg-gradient-primary .text-white-75 {
    color: rgba(255, 255, 255, 0.85) !important;
}

/* Header Layout Improvements */
.card-header.bg-gradient-primary .order-number .badge {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
    font-family: 'Courier New', monospace;
}

.card-header.bg-gradient-primary .order-status .badge {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
    min-width: 120px;
    text-align: center;
}

/* Responsive adjustments */
@media (max-width: 991.98px) {
    .card-header.bg-gradient-primary {
        min-height: auto;
    }

    .card-header.bg-gradient-primary .col-md-6,
    .card-header.bg-gradient-primary .col-md-12 {
        text-align: center !important;
        margin-bottom: 1rem;
    }

    .card-header.bg-gradient-primary .col-md-6:last-child,
    .card-header.bg-gradient-primary .col-md-12:last-child {
        margin-bottom: 0;
    }

    .card-header.bg-gradient-primary .d-flex.flex-column.align-items-end {
        align-items: center !important;
    }
}

/* Other card headers */
.card-header {
    background-color: #f8f9fa !important;
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 1.25rem;
}

.card-header .card-title {
    color: #495057 !important;
    font-weight: 600;
    margin-bottom: 0;
}

.provider-sub-order {
    transition: all 0.3s ease;
}
.provider-sub-order:hover {
    background-color: #f8f9fa;
}
.provider-sub-order:last-child {
    border-bottom: none !important;
}
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}
.item-row {
    padding: 8px 0;
    border-bottom: 1px solid #f1f1f1;
}
.item-row:last-child {
    border-bottom: none;
}
.badge-sm {
    font-size: 0.75em;
    padding: 0.25em 0.5em;
}
.update-provider-status {
    transition: all 0.2s ease;
}
.update-provider-status:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Timeline Styles */
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
}

.timeline-marker-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.timeline-content {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border-left: 4px solid #dee2e6;
    margin-left: 15px;
}

.timeline-header h6 {
    margin-bottom: 5px;
    font-weight: 600;
}

.timeline-body {
    border-top: 1px solid #dee2e6;
    padding-top: 10px;
}

.badge-sm {
    font-size: 0.75em;
    padding: 0.25em 0.5em;
}

/* Status-specific timeline content borders */
.timeline-item:has(.bg-success) .timeline-content {
    border-left-color: #28a745;
}

.timeline-item:has(.bg-danger) .timeline-content {
    border-left-color: #dc3545;
}

.timeline-item:has(.bg-warning) .timeline-content {
    border-left-color: #ffc107;
}

.timeline-item:has(.bg-info) .timeline-content {
    border-left-color: #17a2b8;
}

.timeline-item:has(.bg-primary) .timeline-content {
    border-left-color: #007bff;
}

/* Provider grouping styles */
.provider-status-group {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
.provider-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}
.provider-status-history {
    background: #fff;
}
.provider-status-group:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transition: box-shadow 0.3s ease;
}
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
}

.timeline-marker-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.timeline-content {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border-left: 3px solid #dee2e6;
}

.timeline-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 10px;
}

.timeline-title {
    margin: 0;
    flex-grow: 1;
}

.timeline-body p {
    margin-bottom: 5px;
    font-size: 14px;
}
</style>
<section id="order-details">
    <!-- Order Header -->
    <div class="card shadow-sm border-0">
        <div class="card-header bg-gradient-primary text-white py-3">
            <div class="row align-items-center">
                <div class="col-lg-4 col-md-12 mb-2 mb-lg-0">
                    <div class="d-flex flex-column">
                        <h4 class="card-title mb-1 text-white d-flex align-items-center">
                            <i class="feather icon-file-text mr-2"></i>
                            <?php echo e(__('admin.order_details')); ?>

                        </h4>
                        <small class="text-white-50">
                            <i class="feather icon-calendar mr-1" style="font-size: 0.7rem;"></i>
                            <?php echo e(__('admin.created_at')); ?>: <?php echo e($order->created_at->format('d/m/Y H:i')); ?>

                        </small>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-2 mb-lg-0 text-center text-lg-center">
                    <div class="order-number">
                        <div class="d-flex flex-column align-items-center">
                            <small class="text-white-50 mb-1"><?php echo e(__('admin.order_number')); ?></small>
                            <span class="badge badge-light px-3 py-2" style="font-size: 0.9rem; font-weight: 600;">
                                <i class="feather icon-hash mr-1" style="font-size: 0.8rem;"></i>
                                <?php echo e($order->order_number ?? $order->order_num); ?>

                            </span>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 text-right text-lg-right">
                    <div class="order-status">
                        <?php
                            $statusColors = [
                                'pending_payment' => 'warning',
                                'processing' => 'info',
                                'confirmed' => 'primary',
                                'completed' => 'success',
                                'cancelled' => 'danger',
                                'pending_verification' => 'secondary'
                            ];
                            $color = $statusColors[$order->current_status] ?? 'secondary';

                            $statusIcons = [
                                'pending_payment' => 'clock',
                                'processing' => 'refresh-cw',
                                'confirmed' => 'check-circle',
                                'completed' => 'check-circle-2',
                                'cancelled' => 'x-circle',
                                'pending_verification' => 'help-circle'
                            ];
                            $icon = $statusIcons[$order->current_status] ?? 'circle';
                        ?>
                        <div class="d-flex flex-column align-items-end">
                            <small class="text-white-50 mb-1"><?php echo e(__('admin.status')); ?></small>
                            <span class="badge badge-<?php echo e($color); ?> px-3 py-2" style="font-size: 0.875rem; font-weight: 600;">
                                <i class="feather icon-<?php echo e($icon); ?> mr-1" style="font-size: 0.8rem;"></i>
                                <?php echo e(ucfirst(str_replace('_', ' ', $order->current_status))); ?>

                            </span>
                        </div>
                        <?php if($order->current_status === 'cancelled' && $order->cancelReason): ?>
                            <div class="mt-2 text-right">
                                <small class="text-white-75 d-block">
                                    <i class="feather icon-info mr-1" style="font-size: 0.7rem;"></i>
                                    <?php echo e(__('admin.cancel_reason')); ?>:
                                </small>
                                <?php
                                    $reasonData = $order->cancelReason->reason;
                                    $reasonText = $reasonData
                                ?>
                                <small class="text-white-50 font-italic">
                                    "<?php echo e($reasonText); ?>"
                                </small>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="card-body p-0">
            <div class="row no-gutters">
                <!-- Quick Stats -->
                <div class="col-md-3 border-right">
                    <div class="p-3 text-center">
                        <div class="stat-icon mb-2">
                            <i class="feather icon-dollar-sign text-success" style="font-size: 2rem;"></i>
                        </div>
                        <h5 class="mb-1 font-weight-bold text-success">
                            <?php echo e(number_format($order->total ?? $order->final_total, 2)); ?>

                        </h5>
                        <small class="text-muted"><?php echo e(__('admin.total_amount')); ?></small>
                    </div>
                </div>

                <div class="col-md-3 border-right">
                    <div class="p-3 text-center">
                        <div class="stat-icon mb-2">
                            <?php
                                $paymentStatusColors = [
                                    'pending' => 'warning',
                                    'paid' => 'success',
                                    'failed' => 'danger',
                                    'pending_verification' => 'info',
                                    'refunded' => 'secondary'
                                ];
                                $paymentColor = $paymentStatusColors[$order->payment_status] ?? 'secondary';

                                $paymentIcons = [
                                    'pending' => 'clock',
                                    'paid' => 'check-circle',
                                    'failed' => 'x-circle',
                                    'pending_verification' => 'help-circle',
                                    'refunded' => 'rotate-ccw'
                                ];
                                $paymentIcon = $paymentIcons[$order->payment_status] ?? 'circle';
                            ?>
                            <i class="feather icon-<?php echo e($paymentIcon); ?> text-<?php echo e($paymentColor); ?>" style="font-size: 2rem;"></i>
                        </div>
                        <h6 class="mb-1 font-weight-bold text-<?php echo e($paymentColor); ?>">
                            <?php echo e(ucfirst(str_replace('_', ' ', $order->payment_status))); ?>

                        </h6>
                        <small class="text-muted"><?php echo e(__('admin.payment_status')); ?></small>
                    </div>
                </div>

                <div class="col-md-3 border-right">
                    <div class="p-3 text-center">
                        <div class="stat-icon mb-2">
                            <?php if($order->payment_method === 'wallet'): ?>
                                <i class="feather icon-credit-card text-info" style="font-size: 2rem;"></i>
                            <?php elseif($order->payment_method === 'bank_transfer'): ?>
                                <i class="feather icon-send text-primary" style="font-size: 2rem;"></i>
                            <?php else: ?>
                                <i class="feather icon-credit-card text-secondary" style="font-size: 2rem;"></i>
                            <?php endif; ?>
                        </div>
                        <h6 class="mb-1 font-weight-bold">
                            <?php echo e($paymentMethod ? $paymentMethod->name : 'Unknown'); ?>

                        </h6>
                        <small class="text-muted"><?php echo e(__('admin.payment_method')); ?></small>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="p-3 text-center">
                        <div class="stat-icon mb-2">
                            <i class="feather icon-package text-info" style="font-size: 2rem;"></i>
                        </div>
                        <h6 class="mb-1 font-weight-bold">
                            <?php echo e($order->items ? $order->items->count() : 0); ?>

                        </h6>
                        <small class="text-muted"><?php echo e(__('admin.total_items')); ?></small>
                    </div>
                </div>
            </div>
        </div>

        <?php if($order->current_status === 'request_cancel'): ?>
        <div class="card-footer bg-warning border-top">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="d-flex align-items-center">
                        <i class="feather icon-alert-triangle text-dark mr-2"></i>
                        <span class="text-dark font-weight-bold">
                            <?php echo e(__('admin.cancel_request_pending_review')); ?>

                        </span>
                    </div>
                    <small class="text-dark">
                        <?php echo e(__('admin.cancel_reason')); ?>:
                        <?php if($order->cancelReason): ?>
                            <?php
                                $reasonData = $order->cancelReason->reason;
                                $reasonText = $reasonData ?? 'Unknown';
                            ?>
                            <?php echo e($reasonText); ?>

                        <?php else: ?>
                            <?php echo e(__('admin.no_reason_provided')); ?>

                        <?php endif; ?>
                    </small>
                </div>
                <div class="col-md-4 text-right">
                    <?php
                        $cancelReasonText = __('admin.no_reason_provided');
                        if($order->cancelReason) {
                            $reasonData = $order->cancelReason->reason;
                            $cancelReasonText = $reasonData?? 'Unknown';
                        }
                    ?>
                    <button class="btn btn-success btn-sm accept-cancel-request"
                            data-order-id="<?php echo e($order->id); ?>"
                            data-order-total="<?php echo e($order->total ?? $order->final_total); ?>"
                            data-cancel-reason="<?php echo e($cancelReasonText); ?>">
                        <i class="feather icon-check mr-1"></i>
                        <?php echo e(__('admin.accept')); ?>

                    </button>
                    <button class="btn btn-danger btn-sm reject-cancel-request ml-1"
                            data-order-id="<?php echo e($order->id); ?>">
                        <i class="feather icon-x mr-1"></i>
                        <?php echo e(__('admin.reject')); ?>

                    </button>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>


    <div class="row">
        <!-- Basic Order Information -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title"><?php echo e(__('admin.basic_information')); ?></h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th width="40%"><?php echo e(__('admin.order_number')); ?>:</th>
                            <td><span class="badge badge-info"><?php echo e($order->order_number ?? $order->order_num); ?></span></td>
                        </tr>
                        <tr>
                            <th><?php echo e(__('admin.order_date')); ?>:</th>
                            <td><?php echo e($order->created_at->format('d/m/Y H:i')); ?></td>
                        </tr>
                        <tr>
                            <th><?php echo e(__('admin.booking_type')); ?>:</th>
                            <td>
                                <?php if($order->booking_type): ?>
                                    <span class="badge badge-secondary"><?php echo e(ucfirst($order->booking_type)); ?></span>
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th><?php echo e(__('admin.delivery_type')); ?>:</th>
                            <td>
                                <?php if($order->delivery_type): ?>
                                    <span class="badge badge-secondary"><?php echo e(ucfirst($order->delivery_type)); ?></span>
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php if($order->scheduled_at): ?>
                        <tr>
                            <th><?php echo e(__('admin.scheduled_at')); ?>:</th>
                            <td><?php echo e($order->scheduled_at->format('d/m/Y H:i')); ?></td>
                        </tr>
                        <?php endif; ?>
                        <?php if($order->current_status === 'cancelled' && $order->cancelReason): ?>
                        <tr>
                            <th><?php echo e(__('admin.cancel_reason')); ?>:</th>
                            <td>
                                <?php
                                    $reasonData = $order->cancelReason->reason;
                                    $reasonText = $reasonData ??  'Unknown';
                                ?>
                                <div class="alert alert-danger py-2 px-3 mb-0">
                                    <i class="feather icon-x-circle mr-2"></i>
                                    <span class="font-italic">"<?php echo e($reasonText); ?>"</span>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </table>
                </div>
            </div>
        </div>

        <!-- Customer Information -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title"><?php echo e(__('admin.customer_information')); ?></h5>
                </div>
                <div class="card-body">
                    <?php if($order->user): ?>
                    <table class="table table-borderless">
                        <tr>
                            <th width="40%"><?php echo e(__('admin.name')); ?>:</th>
                            <td><?php echo e($order->user->name); ?></td>
                        </tr>
                        <tr>
                            <th><?php echo e(__('admin.phone')); ?>:</th>
                            <td><?php echo e($order->user->phone); ?></td>
                        </tr>
                        <tr>
                            <th><?php echo e(__('admin.email')); ?>:</th>
                            <td><?php echo e($order->user->email ?? '-'); ?></td>
                        </tr>
                        <?php if($order->user->wallet_balance): ?>
                        <tr>
                            <th><?php echo e(__('admin.wallet_balance')); ?>:</th>
                            <td><?php echo e(number_format($order->user->wallet_balance, 2)); ?> <?php echo e(__('admin.sar')); ?></td>
                        </tr>
                        <?php endif; ?>
                    </table>
                    <?php else: ?>
                    <p class="text-muted"><?php echo e(__('admin.no_customer_info')); ?></p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Provider Sub-Orders -->
    <?php if($order->providerSubOrders && $order->providerSubOrders->count() > 0): ?>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="feather icon-users mr-2"></i>
                        <?php echo e(__('admin.provider_orders')); ?>

                        <span class="badge badge-info ml-2"><?php echo e($order->providerSubOrders->count()); ?></span>
                    </h5>
                    <?php if($order->hasMultipleProviders()): ?>
                        <small class="text-muted"><?php echo e(__('admin.multi_provider_order_note')); ?></small>
                    <?php endif; ?>
                </div>
                <div class="card-body p-0">
                    <?php $__currentLoopData = $order->providerSubOrders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subOrder): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="provider-sub-order border-bottom">
                        <div class="row no-gutters">
                            <!-- Provider Info -->
                            <div class="col-md-4 border-right">
                                <div class="p-3">
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="provider-avatar mr-3">
                                            <div class="avatar-circle bg-primary text-white">
                                                <?php echo e(substr($subOrder->provider->commercial_name ?? $subOrder->provider->user->name, 0, 2)); ?>

                                            </div>
                                        </div>
                                        <div>
                                            <h6 class="mb-0 font-weight-bold"><?php echo e($subOrder->provider->commercial_name ?? $subOrder->provider->user->name); ?></h6>
                                            <small class="text-muted"><?php echo e($subOrder->sub_order_number); ?></small>
                                        </div>
                                    </div>
                                    <div class="provider-details">
                                        <p class="mb-1 small"><i class="feather icon-phone mr-1"></i> <?php echo e($subOrder->provider->user->phone); ?></p>
                                        <p class="mb-1 small"><i class="feather icon-mail mr-1"></i> <?php echo e($subOrder->provider->user->email ?? '-'); ?></p>
                                        <div class="provider-type">
                                            <?php if($subOrder->provider->in_home && $subOrder->provider->in_salon): ?>
                                                <span class="badge badge-success badge-sm"><?php echo e(__('admin.home_and_salon')); ?></span>
                                            <?php elseif($subOrder->provider->in_home): ?>
                                                <span class="badge badge-info badge-sm"><?php echo e(__('admin.home_service')); ?></span>
                                            <?php elseif($subOrder->provider->in_salon): ?>
                                                <span class="badge badge-primary badge-sm"><?php echo e(__('admin.salon_service')); ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Order Items -->
                            <div class="col-md-5 border-right">
                                <div class="p-3">
                                    <h6 class="mb-2"><?php echo e(__('admin.order_items')); ?></h6>
                                    <?php
                                        // Get items for this provider - try relationship first, then fallback to filtering main order items
                                        $providerItems = $subOrder->orderItems;
                                        if ($providerItems->isEmpty()) {
                                            $providerItems = $order->items->filter(function($item) use ($subOrder) {
                                                if ($item->item && isset($item->item->provider_id)) {
                                                    return $item->item->provider_id == $subOrder->provider_id;
                                                }
                                                return false;
                                            });
                                        }
                                    ?>

                                    <?php if($providerItems && $providerItems->count() > 0): ?>
                                        <div class="items-list">
                                            <?php $__currentLoopData = $providerItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="item-row d-flex justify-content-between align-items-center mb-2">
                                                <div class="item-info">
                                                    <span class="item-name font-weight-medium"><?php echo e($item->name); ?></span>
                                                    <?php if($item->item_type === 'App\Models\Service'): ?>
                                                        <span class="badge badge-info badge-sm ml-1"><?php echo e(__('admin.service')); ?></span>
                                                    <?php else: ?>
                                                        <span class="badge badge-success badge-sm ml-1"><?php echo e(__('admin.product')); ?></span>
                                                    <?php endif; ?>
                                                    <br>
                                                    <small class="text-muted"><?php echo e(__('admin.qty')); ?>: <?php echo e($item->quantity); ?> × <?php echo e(number_format($item->price, 2)); ?></small>
                                                </div>
                                                <div class="item-total">
                                                    <strong><?php echo e(number_format($item->total, 2)); ?></strong>
                                                </div>
                                            </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    <?php else: ?>
                                        <p class="text-muted small"><?php echo e(__('admin.no_items')); ?></p>
                                        
                                        <small class="text-danger">Debug: Provider ID <?php echo e($subOrder->provider_id); ?>, Total Order Items: <?php echo e($order->items->count()); ?></small>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Status & Actions -->
                            <div class="col-md-3">
                                <div class="p-3">
                                    <div class="status-section mb-3">
                                        <?php
                                            $statusColors = [
                                                'pending_payment' => 'warning',
                                                'processing' => 'info',
                                                'confirmed' => 'primary',
                                                'completed' => 'success',
                                                'cancelled' => 'danger'
                                            ];
                                            $color = $statusColors[$subOrder->status] ?? 'secondary';
                                        ?>
                                        <div class="d-flex align-items-center justify-content-between mb-2">
                                            <span class="badge badge-<?php echo e($color); ?>">
                                                <?php echo e(ucfirst(str_replace('_', ' ', $subOrder->status))); ?>

                                            </span>
                                        </div>

                                        <?php if($subOrder->status === 'cancelled' && $order->cancelReason): ?>
                                        <div class="cancellation-reason mb-2">
                                            <small class="text-danger d-block">
                                                <i class="feather icon-info mr-1"></i>
                                                <?php echo e(__('admin.cancel_reason')); ?>:
                                            </small>
                                            <?php
                                                $reasonData = $order->cancelReason->reason;
                                                $reasonText = $reasonData ?? 'Unknown';
                                            ?>
                                            <small class="text-muted font-italic">
                                                "<?php echo e($reasonText); ?>"
                                            </small>
                                        </div>
                                        <?php endif; ?>

                                        <!-- Provider Total -->
                                        <div class="provider-total mb-2">
                                            <small class="text-muted"><?php echo e(__('admin.provider_total')); ?>:</small>
                                            <div class="font-weight-bold text-success"><?php echo e(number_format($subOrder->total, 2)); ?> <?php echo e(__('admin.sar')); ?></div>
                                        </div>

                                        <!-- Status Update Actions -->
                                        <?php if(in_array($subOrder->status, ['processing', 'confirmed'])): ?>
                                        <div class="actions">
                                            <button class="btn btn-success btn-sm btn-block update-provider-status"
                                                    data-order-id="<?php echo e($order->id); ?>"
                                                    data-provider-id="<?php echo e($subOrder->provider_id); ?>"
                                                    data-status="completed">
                                                <i class="feather icon-check mr-1"></i>
                                                <?php echo e(__('admin.mark_completed')); ?>

                                            </button>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="row">
        <!-- Address Information -->
        <?php if($order->address): ?>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title"><?php echo e(__('admin.address_information')); ?></h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th width="40%"><?php echo e(__('admin.address')); ?>:</th>
                            <td><?php echo e($order->address->details ?? '-'); ?></td>
                        </tr>

                        <tr>
                            <th><?php echo e(__('admin.phone')); ?>:</th>
                            <td><?php echo e($order->address->phone ?? '-'); ?></td>
                        </tr>
                        <?php if($order->address->latitude && $order->address->longitude): ?>
                        <tr>
                            <th><?php echo e(__('admin.location')); ?>:</th>
                            <td>
                                <a href="https://maps.google.com/?q=<?php echo e($order->address->latitude); ?>,<?php echo e($order->address->longitude); ?>"
                                   target="_blank" class="btn btn-sm btn-outline-primary">
                                    <i class="feather icon-map-pin"></i> <?php echo e(__('admin.view_on_map')); ?>

                                </a>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </table>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>



    <div class="row">
        <!-- Financial Breakdown -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title"><?php echo e(__('admin.financial_breakdown')); ?></h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th width="50%"><?php echo e(__('admin.subtotal')); ?>:</th>
                            <td class="text-right"><?php echo e(number_format($order->subtotal ?? 0, 2)); ?> <?php echo e(__('admin.sar')); ?></td>
                        </tr>
                        <?php if($order->services_total > 0): ?>
                        <tr>
                            <th><?php echo e(__('admin.services_total')); ?>:</th>
                            <td class="text-right"><?php echo e(number_format($order->services_total, 2)); ?> <?php echo e(__('admin.sar')); ?></td>
                        </tr>
                        <?php endif; ?>
                        <?php if($order->products_total > 0): ?>
                        <tr>
                            <th><?php echo e(__('admin.products_total')); ?>:</th>
                            <td class="text-right"><?php echo e(number_format($order->products_total, 2)); ?> <?php echo e(__('admin.sar')); ?></td>
                        </tr>
                        <?php endif; ?>
                        <?php if($order->booking_fee > 0): ?>
                        <tr>
                            <th><?php echo e(__('admin.booking_fee')); ?>:</th>
                            <td class="text-right"><?php echo e(number_format($order->booking_fee, 2)); ?> <?php echo e(__('admin.sar')); ?></td>
                        </tr>
                        <?php endif; ?>
                        <?php if($order->home_service_fee > 0): ?>
                        <tr>
                            <th><?php echo e(__('admin.home_service_fee')); ?>:</th>
                            <td class="text-right"><?php echo e(number_format($order->home_service_fee, 2)); ?> <?php echo e(__('admin.sar')); ?></td>
                        </tr>
                        <?php endif; ?>
                        <?php if($order->delivery_fee > 0): ?>
                        <tr>
                            <th><?php echo e(__('admin.delivery_fee')); ?>:</th>
                            <td class="text-right"><?php echo e(number_format($order->delivery_fee, 2)); ?> <?php echo e(__('admin.sar')); ?></td>
                        </tr>
                        <?php endif; ?>
                        <?php if($order->discount_amount > 0): ?>
                        <tr class="text-success">
                            <th><?php echo e(__('admin.discount')); ?>:</th>
                            <td class="text-right">-<?php echo e(number_format($order->discount_amount, 2)); ?> <?php echo e(__('admin.sar')); ?></td>
                        </tr>
                        <?php endif; ?>
                        <?php if($order->loyalty_points_used > 0): ?>
                        <tr class="text-success">
                            <th><?php echo e(__('admin.loyalty_points_used')); ?>:</th>
                            <td class="text-right">-<?php echo e(number_format($order->loyalty_points_used, 2)); ?> <?php echo e(__('admin.sar')); ?></td>
                        </tr>
                        <?php endif; ?>
                        <tr class="border-top">
                            <th><strong><?php echo e(__('admin.total')); ?>:</strong></th>
                            <td class="text-right"><strong><?php echo e(number_format($order->total ?? $order->final_total, 2)); ?> <?php echo e(__('admin.sar')); ?></strong></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- Payment Information -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title"><?php echo e(__('admin.payment_information')); ?></h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th width="50%"><?php echo e(__('admin.payment_method')); ?>:</th>
                            <td>
                                <span class="badge badge-secondary">
                                    <?php echo e($paymentMethod ? $paymentMethod->name : 'Unknown'); ?>

                                </span>
                            </td>
                        </tr>
                        <tr>
                            <th><?php echo e(__('admin.payment_status')); ?>:</th>
                            <td>
                                <?php
                                    $paymentStatusColors = [
                                        'pending' => 'warning',
                                        'paid' => 'success',
                                        'failed' => 'danger',
                                        'pending_verification' => 'info',
                                        'refunded' => 'secondary'
                                    ];
                                    $paymentColor = $paymentStatusColors[$order->payment_status] ?? 'secondary';
                                ?>
                                <span class="badge badge-<?php echo e($paymentColor); ?>">
                                    <?php echo e(ucfirst(str_replace('_', ' ', $order->payment_status))); ?>

                                </span>
                            </td>
                        </tr>
                        <?php if($order->payment_reference): ?>
                        <tr>
                            <th><?php echo e(__('admin.payment_reference')); ?>:</th>
                            <td><code><?php echo e($order->payment_reference); ?></code></td>
                        </tr>
                        <?php endif; ?>
                        <?php if($order->payment_date): ?>
                        <tr>
                            <th><?php echo e(__('admin.payment_date')); ?>:</th>
                            <td><?php echo e($order->payment_date?->format('d/m/Y H:i')); ?></td>
                        </tr>
                        <?php endif; ?>
                        <?php if($order->amount_paid > 0): ?>
                        <tr>
                            <th><?php echo e(__('admin.amount_paid')); ?>:</th>
                            <td><strong><?php echo e(number_format($order->amount_paid, 2)); ?> <?php echo e(__('admin.sar')); ?></strong></td>
                        </tr>
                        <?php endif; ?>
                        <?php if($order->coupon_code): ?>
                        <tr>
                            <th><?php echo e(__('admin.coupon_code')); ?>:</th>
                            <td>
                                <span class="badge badge-success"><?php echo e($order->coupon_code); ?></span>
                                <?php if($order->coupon): ?>
                                    <br><small class="text-muted"><?php echo e($order->coupon->name ?? ''); ?></small>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Bank Transfer Details -->
    <?php if($order->payment_method_id === 5 && $order->bankTransfer): ?>
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0"><?php echo e(__('admin.bank_transfer_details')); ?></h5>
            <div>
                <?php if($order->bankTransfer->status === 'pending'): ?>
                    <button class="btn btn-success btn-sm verify-transfer" data-order-id="<?php echo e($order->id); ?>">
                        <i class="feather icon-check"></i> <?php echo e(__('admin.verify_transfer')); ?>

                    </button>
                    <button class="btn btn-danger btn-sm reject-transfer" data-order-id="<?php echo e($order->id); ?>">
                        <i class="feather icon-x"></i> <?php echo e(__('admin.reject_transfer')); ?>

                    </button>
                <?php else: ?>
                    <span class="badge badge-<?php echo e($order->bankTransfer->status === 'verified' ? 'success' : 'danger'); ?>">
                        <?php echo e(ucfirst($order->bankTransfer->status)); ?>

                    </span>
                <?php endif; ?>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <th width="50%"><?php echo e(__('admin.sender_bank_name')); ?>:</th>
                            <td><?php echo e($order->bankTransfer->sender_bank_name); ?></td>
                        </tr>
                        <tr>
                            <th><?php echo e(__('admin.sender_account_holder')); ?>:</th>
                            <td><?php echo e($order->bankTransfer->sender_account_holder_name); ?></td>
                        </tr>
                        <tr>
                            <th><?php echo e(__('admin.sender_account_number')); ?>:</th>
                            <td><code><?php echo e($order->bankTransfer->sender_account_number); ?></code></td>
                        </tr>
                        <?php if($order->bankTransfer->sender_iban): ?>
                        <tr>
                            <th><?php echo e(__('admin.sender_iban')); ?>:</th>
                            <td><code><?php echo e($order->bankTransfer->sender_iban); ?></code></td>
                        </tr>
                        <?php endif; ?>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <th width="50%"><?php echo e(__('admin.transfer_amount')); ?>:</th>
                            <td><strong><?php echo e(number_format($order->bankTransfer->transfer_amount, 2)); ?> <?php echo e(__('admin.sar')); ?></strong></td>
                        </tr>
                        <tr>
                            <th><?php echo e(__('admin.transfer_date')); ?>:</th>
                            <td><?php echo e($order->payment_date?->format('d/m/Y')); ?></td>
                        </tr>
                        <?php if($order->bankTransfer->transfer_reference): ?>
                        <tr>
                            <th><?php echo e(__('admin.transfer_reference')); ?>:</th>
                            <td><code><?php echo e($order->bankTransfer->transfer_reference); ?></code></td>
                        </tr>
                        <?php endif; ?>
                        <?php if($order->bankTransfer->admin_notes): ?>
                        <tr>
                            <th><?php echo e(__('admin.admin_notes')); ?>:</th>
                            <td><?php echo e($order->bankTransfer->admin_notes); ?></td>
                        </tr>
                        <?php endif; ?>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Order Status History -->
    <?php if($order->statusChanges && $order->statusChanges->count() > 0): ?>
    <div class="card">
        <div class="card-header">
            <h5 class="card-title"><?php echo e(__('admin.order_status_history')); ?></h5>
        </div>
        <div class="card-body">
            <div class="timeline">
                <?php $__currentLoopData = $order->statusChanges; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $statusChange): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="timeline-item">
                    <div class="timeline-marker">
                        <?php
                            $statusColors = [
                                'pending_payment' => 'warning',
                                'processing' => 'info',
                                'confirmed' => 'primary',
                                'completed' => 'success',
                                'cancelled' => 'danger',
                                'new' => 'secondary'
                            ];
                            $color = $statusColors[$statusChange->status] ?? 'secondary';
                        ?>
                        <div class="timeline-marker-icon bg-<?php echo e($color); ?>">
                            <?php switch($statusChange->status):
                                case ('completed'): ?>
                                    <i class="feather icon-check text-white"></i>
                                    <?php break; ?>
                                <?php case ('cancelled'): ?>
                                    <i class="feather icon-x text-white"></i>
                                    <?php break; ?>
                                <?php case ('processing'): ?>
                                    <i class="feather icon-clock text-white"></i>
                                    <?php break; ?>
                                <?php default: ?>
                                    <i class="feather icon-circle text-white"></i>
                            <?php endswitch; ?>
                        </div>
                    </div>
                    <div class="timeline-content">
                        <div class="timeline-header">
                            <h6 class="timeline-title">
                                <span class="badge badge-<?php echo e($color); ?>">
                                    <?php echo e(ucfirst(str_replace('_', ' ', $statusChange->status))); ?>

                                </span>
                            </h6>
                            <small class="text-muted">
                                <?php echo e($statusChange->created_at->format('d/m/Y H:i')); ?>

                            </small>
                        </div>
                        <div class="timeline-body">
                            <p class="mb-1">
                                <strong><?php echo e(__('admin.changed_by')); ?>:</strong>
                                <?php echo e($statusChange->changed_by_name); ?>

                            </p>
                            <?php if($statusChange->map_desc): ?>
                            <p class="mb-0">
                                <strong><?php echo e(__('admin.notes')); ?>:</strong>
                                <?php echo e($statusChange->map_desc); ?>

                            </p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Order Status History -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title">
                <i class="feather icon-clock mr-2"></i>
                <?php echo e(__('admin.provider_orders_status_history')); ?>

            </h5>
            <small class="text-muted"><?php echo e(__('admin.provider_sub_orders_audit_trail')); ?></small>
        </div>
        <div class="card-body">
            <?php if($order->providerSubOrders && $order->providerSubOrders->count() > 0): ?>
                <?php $__currentLoopData = $order->providerSubOrders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subOrder): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php
                        // Get status changes for this specific sub-order
                        $subOrderStatuses = \App\Models\OrderStatus::where('provider_sub_order_id', $subOrder->id)
                            ->with(['statusable'])
                            ->orderBy('created_at', 'desc')
                            ->get();
                    ?>

                    <div class="provider-status-group mb-4">
                        <!-- Provider Header -->
                        <div class="provider-header bg-light p-3 rounded-top border">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="d-flex align-items-center">
                                    <div class="provider-avatar mr-3">
                                        <div class="avatar-circle bg-primary text-white">
                                            <?php echo e(substr($subOrder->provider->commercial_name ?? $subOrder->provider->user->name, 0, 2)); ?>

                                        </div>
                                    </div>
                                    <div>
                                        <h6 class="mb-0 font-weight-bold">
                                            <?php echo e($subOrder->provider->commercial_name ?? $subOrder->provider->user->name); ?>

                                        </h6>
                                        <small class="text-muted">
                                            <?php echo e(__('admin.sub_order')); ?>: <?php echo e($subOrder->sub_order_number); ?>

                                        </small>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <?php
                                        $currentStatusColors = [
                                            'new' => 'secondary',
                                            'processing' => 'info',
                                            'confirmed' => 'primary',
                                            'completed' => 'success',
                                            'cancelled' => 'danger'
                                        ];
                                        $currentColor = $currentStatusColors[$subOrder->status] ?? 'secondary';
                                    ?>
                                    <span class="badge badge-<?php echo e($currentColor); ?> px-2 py-1">
                                        <?php echo e(ucfirst(str_replace('_', ' ', $subOrder->status))); ?>

                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Status History for this Provider -->
                        <div class="provider-status-history border border-top-0 rounded-bottom">
                            <?php if($subOrderStatuses->count() > 0): ?>
                                <div class="timeline p-3">
                                    <?php $__currentLoopData = $subOrderStatuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $statusChange): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="timeline-item">
                                        <div class="timeline-marker">
                                            <?php
                                                $statusColors = [
                                                    'new' => 'secondary',
                                                    'processing' => 'info',
                                                    'confirmed' => 'primary',
                                                    'completed' => 'success',
                                                    'cancelled' => 'danger'
                                                ];
                                                $color = $statusColors[$statusChange->status] ?? 'secondary';
                                            ?>
                                            <div class="timeline-marker-icon bg-<?php echo e($color); ?>">
                                                <?php if($statusChange->status === 'completed'): ?>
                                                    <i class="feather icon-check text-white"></i>
                                                <?php elseif($statusChange->status === 'cancelled'): ?>
                                                    <i class="feather icon-x text-white"></i>
                                                <?php elseif($statusChange->status === 'processing'): ?>
                                                    <i class="feather icon-play text-white"></i>
                                                <?php elseif($statusChange->status === 'confirmed'): ?>
                                                    <i class="feather icon-check-circle text-white"></i>
                                                <?php else: ?>
                                                    <i class="feather icon-clock text-white"></i>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div class="timeline-content">
                                            <div class="timeline-header">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <h6 class="mb-1">
                                                            <span class="badge badge-<?php echo e($color); ?> mr-2">
                                                                <?php echo e(ucfirst(str_replace('_', ' ', $statusChange->status))); ?>

                                                            </span>
                                                        </h6>
                                                        <small class="text-muted">
                                                            <i class="feather icon-calendar mr-1"></i>
                                                            <?php echo e($statusChange->created_at->format('M d, Y H:i:s')); ?>

                                                            <span class="mx-2">•</span>
                                                            <i class="feather icon-user mr-1"></i>
                                                            <?php if($statusChange->statusable_type === 'App\Models\Admin'): ?>
                                                                <?php echo e(__('admin.admin')); ?>: <?php echo e($statusChange->statusable->name ?? 'System'); ?>

                                                            <?php elseif($statusChange->statusable_type === 'App\Models\User'): ?>
                                                                <?php echo e(__('admin.customer')); ?>: <?php echo e($statusChange->statusable->name ?? 'Customer'); ?>

                                                            <?php elseif($statusChange->statusable_type === 'App\Models\Provider'): ?>
                                                                <?php echo e(__('admin.provider')); ?>: <?php echo e($statusChange->statusable->user->name ?? 'Provider'); ?>

                                                            <?php else: ?>
                                                                <?php echo e(__('admin.system')); ?>

                                                            <?php endif; ?>
                                                        </small>
                                                    </div>
                                                    <small class="text-muted">
                                                        <?php echo e($statusChange->created_at->diffForHumans()); ?>

                                                    </small>
                                                </div>
                                            </div>
                                            <?php if($statusChange->map_desc): ?>
                                            <div class="timeline-body mt-2">
                                                <p class="mb-0 text-muted">
                                                    <i class="feather icon-message-circle mr-1"></i>
                                                    <?php echo e($statusChange->map_desc); ?>

                                                </p>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="feather icon-clock text-muted" style="font-size: 24px;"></i>
                                    <p class="mt-2 text-muted mb-0"><?php echo e(__('admin.no_status_changes_for_provider')); ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
                <div class="text-center py-4">
                    <i class="feather icon-clock text-muted" style="font-size: 48px;"></i>
                    <h6 class="mt-3 text-muted"><?php echo e(__('admin.no_provider_orders')); ?></h6>
                    <p class="text-muted"><?php echo e(__('admin.provider_orders_will_appear_here')); ?></p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="card">
        <div class="card-body text-center">
            <a href="<?php echo e(route('admin.orders.index')); ?>" class="btn btn-secondary">
                <i class="feather icon-arrow-left"></i> <?php echo e(__('admin.back_to_orders')); ?>

            </a>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('js'); ?>
<!-- SweetAlert2 JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.all.min.js"></script>

<script>
function updateOrderStatus(orderId, status) {
    Swal.fire({
        title: '<?php echo e(__("admin.are_you_sure")); ?>',
        text: '<?php echo e(__("admin.confirm_status_change")); ?>',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: '<?php echo e(__("admin.yes_update")); ?>',
        cancelButtonText: '<?php echo e(__("admin.cancel")); ?>'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: '<?php echo e(__("admin.updating")); ?>',
                text: '<?php echo e(__("admin.please_wait")); ?>',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading()
                }
            });

            // Add AJAX call to update order status
            fetch(`/admin/orders/${orderId}/status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ status: status })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        title: '<?php echo e(__("admin.success")); ?>',
                        text: '<?php echo e(__("admin.status_updated_successfully")); ?>',
                        icon: 'success',
                        confirmButtonText: '<?php echo e(__("admin.ok")); ?>'
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        title: '<?php echo e(__("admin.error")); ?>',
                        text: data.message || '<?php echo e(__("admin.error_occurred")); ?>',
                        icon: 'error',
                        confirmButtonText: '<?php echo e(__("admin.ok")); ?>'
                    });
                }
            })
            .catch(error => {
                Swal.fire({
                    title: '<?php echo e(__("admin.error")); ?>',
                    text: '<?php echo e(__("admin.error_occurred")); ?>',
                    icon: 'error',
                    confirmButtonText: '<?php echo e(__("admin.ok")); ?>'
                });
            });
        }
    });
}

function markPaymentAsPaid(orderId) {
    Swal.fire({
        title: '<?php echo e(__("admin.confirm_mark_payment_paid")); ?>',
        text: '<?php echo e(__("admin.payment_will_be_marked_paid")); ?>',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#d33',
        confirmButtonText: '<?php echo e(__("admin.yes_mark_paid")); ?>',
        cancelButtonText: '<?php echo e(__("admin.cancel")); ?>'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: '<?php echo e(__("admin.processing")); ?>',
                text: '<?php echo e(__("admin.please_wait")); ?>',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading()
                }
            });

            fetch(`/admin/orders/${orderId}/mark-payment-paid`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        title: '<?php echo e(__("admin.success")); ?>',
                        text: '<?php echo e(__("admin.payment_marked_paid_successfully")); ?>',
                        icon: 'success',
                        confirmButtonText: '<?php echo e(__("admin.ok")); ?>'
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        title: '<?php echo e(__("admin.error")); ?>',
                        text: data.message || '<?php echo e(__("admin.error_occurred")); ?>',
                        icon: 'error',
                        confirmButtonText: '<?php echo e(__("admin.ok")); ?>'
                    });
                }
            })
            .catch(error => {
                Swal.fire({
                    title: '<?php echo e(__("admin.error")); ?>',
                    text: '<?php echo e(__("admin.error_occurred")); ?>',
                    icon: 'error',
                    confirmButtonText: '<?php echo e(__("admin.ok")); ?>'
                });
            });
        }
    });
}

function updateProviderStatus(orderId, providerId, status) {
    Swal.fire({
        title: '<?php echo e(__("admin.are_you_sure")); ?>',
        text: '<?php echo e(__("admin.confirm_provider_status_change")); ?>',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: '<?php echo e(__("admin.yes_update")); ?>',
        cancelButtonText: '<?php echo e(__("admin.cancel")); ?>'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: '<?php echo e(__("admin.updating")); ?>',
                text: '<?php echo e(__("admin.please_wait")); ?>',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading()
                }
            });

            // Add AJAX call to update provider status
            fetch(`/admin/orders/${orderId}/provider/${providerId}/status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ status: status })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        title: '<?php echo e(__("admin.success")); ?>',
                        text: '<?php echo e(__("admin.provider_status_updated_successfully")); ?>',
                        icon: 'success',
                        confirmButtonText: '<?php echo e(__("admin.ok")); ?>'
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        title: '<?php echo e(__("admin.error")); ?>',
                        text: data.message || '<?php echo e(__("admin.error_occurred")); ?>',
                        icon: 'error',
                        confirmButtonText: '<?php echo e(__("admin.ok")); ?>'
                    });
                }
            })
            .catch(error => {
                Swal.fire({
                    title: '<?php echo e(__("admin.error")); ?>',
                    text: '<?php echo e(__("admin.error_occurred")); ?>',
                    icon: 'error',
                    confirmButtonText: '<?php echo e(__("admin.ok")); ?>'
                });
            });
        }
    });
}

// Cancel request handling
document.addEventListener('DOMContentLoaded', function() {
    // Accept Cancel Request
    document.querySelectorAll('.accept-cancel-request').forEach(button => {
        button.addEventListener('click', function() {
            const orderId = this.dataset.orderId;
            const orderTotal = this.dataset.orderTotal;
            const cancelReason = this.dataset.cancelReason || '<?php echo e(__("admin.no_reason_provided")); ?>';

            Swal.fire({
                title: '<?php echo e(__("admin.accept_cancel_request")); ?>',
                html: `
                    <div class="text-left">
                        <p><?php echo e(__("admin.are_you_sure_accept_cancel_request")); ?></p>
                        <div class="form-group mt-3">
                            <label for="cancel_fees"><?php echo e(__("admin.cancel_fees")); ?> (<?php echo e(__("admin.sar")); ?>) *</label>
                            <input type="number" id="cancel_fees" class="form-control" min="0" max="${orderTotal}" step="0.01" value="<?php echo e($cancellationFeeAmount); ?>" required>
                            <small class="text-muted"><?php echo e(__("admin.total")); ?>: ${orderTotal} <?php echo e(__("admin.sar")); ?></small>
                        </div>
                    </div>
                `,
                type: 'question',
                showCancelButton: true,
                confirmButtonText: '<?php echo e(__("admin.accept")); ?>',
                cancelButtonText: '<?php echo e(__("admin.cancel")); ?>',
                confirmButtonColor: '#28a745',
                preConfirm: () => {
                    const cancelFees = document.getElementById('cancel_fees').value;

                    if (!cancelFees || cancelFees === '' || isNaN(cancelFees)) {
                        Swal.showValidationMessage('<?php echo e(__("admin.cancel_fees_required")); ?>');
                        return false;
                    }

                    if (parseFloat(cancelFees) < 0) {
                        Swal.showValidationMessage('<?php echo e(__("admin.cancel_fees_must_be_positive")); ?>');
                        return false;
                    }

                    if (parseFloat(cancelFees) > parseFloat(orderTotal)) {
                        Swal.showValidationMessage('<?php echo e(__("admin.cancel_fees_cannot_exceed_total")); ?>');
                        return false;
                    }

                    return {
                        cancel_fees: parseFloat(cancelFees)
                    };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading
                    Swal.fire({
                        title: '<?php echo e(__("admin.processing")); ?>',
                        text: '<?php echo e(__("admin.please_wait")); ?>',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading()
                        }
                    });

                    fetch(`/admin/cancel-request-orders/${orderId}/accept`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            cancel_fees: result.value.cancel_fees
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire({
                                title: '<?php echo e(__("admin.success")); ?>',
                                html: `
                                    <p>${data.message}</p>
                                    <div class="mt-2">
                                        <strong><?php echo e(__("admin.refund_amount")); ?>:</strong> ${data.data.refund_amount} <?php echo e(__("admin.sar")); ?><br>
                                        <strong><?php echo e(__("admin.cancel_fees")); ?>:</strong> ${data.data.cancel_fees} <?php echo e(__("admin.sar")); ?>

                                    </div>
                                `,
                                type: 'success',
                                confirmButtonText: '<?php echo e(__("admin.ok")); ?>'
                            }).then(() => {
                                window.location.href = `/admin/orders/${orderId}/show`;
                            });
                        } else {
                            Swal.fire({
                                title: '<?php echo e(__("admin.error")); ?>',
                                text: data.message || '<?php echo e(__("admin.error_occurred")); ?>',
                                type: 'error',
                                confirmButtonText: '<?php echo e(__("admin.ok")); ?>'
                            });
                        }
                    })
                    .catch(error => {
                        Swal.fire({
                            title: '<?php echo e(__("admin.error")); ?>',
                            text: '<?php echo e(__("admin.error_occurred")); ?>',
                            type: 'error',
                            confirmButtonText: '<?php echo e(__("admin.ok")); ?>'
                        });
                    });
                }
            });
        });
    });

    // Reject Cancel Request
    document.querySelectorAll('.reject-cancel-request').forEach(button => {
        button.addEventListener('click', function() {
            const orderId = this.dataset.orderId;

            Swal.fire({
                title: '<?php echo e(__("admin.reject_cancel_request")); ?>',
                text: '<?php echo e(__("admin.are_you_sure_reject_cancel_request")); ?>',
                type: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<?php echo e(__("admin.reject")); ?>',
                cancelButtonText: '<?php echo e(__("admin.cancel")); ?>'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading
                    Swal.fire({
                        title: '<?php echo e(__("admin.processing")); ?>',
                        text: '<?php echo e(__("admin.please_wait")); ?>',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading()
                        }
                    });

                    fetch(`/admin/cancel-request-orders/${orderId}/reject`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({ reason: 'Cancel request rejected by admin' })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire({
                                title: '<?php echo e(__("admin.success")); ?>',
                                text: data.message,
                                type: 'success',
                                confirmButtonText: '<?php echo e(__("admin.ok")); ?>'
                            }).then(() => {
                                window.location.href = `/admin/orders/${orderId}/Show`;
                            });
                        } else {
                            Swal.fire({
                                title: '<?php echo e(__("admin.error")); ?>',
                                text: data.message || '<?php echo e(__("admin.error_occurred")); ?>',
                                type: 'error',
                                confirmButtonText: '<?php echo e(__("admin.ok")); ?>'
                            });
                        }
                    })
                    .catch(error => {
                        Swal.fire({
                            title: '<?php echo e(__("admin.error")); ?>',
                            text: '<?php echo e(__("admin.error_occurred")); ?>',
                            type: 'error',
                            confirmButtonText: '<?php echo e(__("admin.ok")); ?>'
                        });
                    });
                }
            });
        });
    });
});
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layout.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Workstation\Taswk\sorriso-backend\resources\views/admin/cancel_request_orders/show.blade.php ENDPATH**/ ?>