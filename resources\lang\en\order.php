<?php

return [
  'types_by_user'             => 'By User',
  'types_by_provider'         => 'By Provider',
  'status_new'                => 'New',
  'status_accepted'           => 'Accepted',
  'status_refused'            => 'Refused',
  'status_cancel'             => 'Cancelled',
  'status_cancelled'          => 'Canceled',
  'status_finished'           => 'Finished',
  'status_in_progress'        => 'In Progress',
  'status_delivered'          => 'Delivered',
  'status_pending'            => 'Pending',
  'pay_type_undefined'        => 'Undefined',
  'pay_type_mada'             => 'Cash (mada)',
  'pay_type_cod'              => 'Cash on Delivery',
  'pay_type_visa'             => 'Visa',
  'pay_type_applepay'         => 'Apple Pay',
  'pay_type_googlepay'        => 'Google Pay',
  'pay_type_tabby'            => 'Tabby',
  'pay_type_tamara'           => 'Tamara',
  'pay_status_pending'        => 'Pending',
  'pay_status_downpayment'    => 'Downpayment',
  'pay_status_done'           => 'Done',
  'pay_status_returned'       => 'Returned',

  // Order Status Values
  'new'                       => 'New',
  'accepted'                  => 'Accepted',
  'in_progress'               => 'In Progress',
  'delivered'                 => 'Delivered',
  'cancelled'                 => 'Cancelled',
  'pending_payment'           => 'Pending Payment',
  'processing'                => 'Processing',
  'confirmed'                 => 'Confirmed',
  'completed'                 => 'Completed',
  'partially_cancelled'       => 'Partially Cancelled',
  'pending_verification'      => 'Pending Verification',
  'request_cancel'            => 'Cancel Request',

  // Error messages
  'pending_payment_exists'  => 'You have a pending payment order. Please complete the payment or wait for it to expire before creating a new order.',
];