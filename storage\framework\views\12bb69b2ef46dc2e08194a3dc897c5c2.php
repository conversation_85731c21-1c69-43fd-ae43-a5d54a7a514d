<div class="position-relative">
    
    
    

    
    <table class="table " id="tab">
        <thead>
            <tr>
                <th>
                    <label class="container-checkbox">
                        <input type="checkbox" value="value1" name="name1" id="checkedAll">
                        <span class="checkmark"></span>
                    </label>
                </th>
                <th><?php echo e(__('admin.id_num')); ?></th>
                <th><?php echo e(__('admin.image')); ?></th>
                <th><?php echo e(__('admin.name')); ?></th>
                <th><?php echo e(__('admin.email')); ?></th>
                <th><?php echo e(__('admin.phone')); ?></th>
                <th><?php echo e(__('admin.phone_status')); ?></th>
                <th><?php echo e(__('admin.status')); ?></th>

                <th><?php echo e(__('admin.control')); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $rows; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr class="delete_row">
                <td class="text-center">
                    <label class="container-checkbox">
                    <input type="checkbox" class="checkSingle" id="<?php echo e($row->id); ?>">
                    <span class="checkmark"></span>
                    </label>
                </td>
                <td><?php echo e($row->id); ?></td>
                <td><img src="<?php echo e($row->image); ?>" width="30px" height="30px" alt=""></td>
                <td><?php echo e($row->name); ?></td>
                <td><?php echo e($row->email); ?></td>
                <td><?php echo e($row->phone); ?></td>
                <td>
                    <?php if($row->is_active): ?>
                    <span class="btn btn-sm round btn-outline-success">
                        <?php echo e(__('admin.activate')); ?> <i class="la la-close font-medium-2"></i>
                    </span>
                    <?php else: ?>
                    <span class="btn btn-sm round btn-outline-danger">
                        <?php echo e(__('admin.dis_activate')); ?> <i class="la la-check font-medium-2"></i>
                    </span>
                    <?php endif; ?>
                </td>
                <td>
                    <?php if($row->status == 'blocked'): ?>
                        <span class="btn btn-sm round btn-outline-success block_user" data-id="<?php echo e($row->id); ?>"><?php echo e(__('admin.unblock')); ?></span>
                    <?php else: ?>
                        <span class="btn btn-sm round btn-outline-danger block_user" data-id="<?php echo e($row->id); ?>"><?php echo e(__('admin.block')); ?></span>
                    <?php endif; ?>
                </td>
                   <td class="product-action">
                    <span class="text-primary"><a href="<?php echo e(route('admin.clients.show', ['id' => $row->id])); ?>" class="btn btn-warning btn-sm p-1" title="<?php echo e(__('admin.show')); ?>"><i class="feather icon-eye"></i></a></span>
                    <span class="action-edit text-primary"><a href="<?php echo e(route('admin.clients.edit', ['id' => $row->id])); ?>" class="btn btn-primary btn-sm p-1" title="<?php echo e(__('admin.edit')); ?>"><i class="feather icon-edit"></i></a></span>
                     

                </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>
    
    
    <?php if($rows->count() == 0): ?>
        <div class="d-flex flex-column w-100 align-center mt-4">
            <img src="<?php echo e(asset('admin/app-assets/images/pages/404.png')); ?>" alt="">
            <span class="mt-2" style="font-family: cairo"><?php echo e(__('admin.there_are_no_matches_matching')); ?></span>
        </div>
    <?php endif; ?>
    

</div>

<?php if($rows->count() > 0 && $rows instanceof \Illuminate\Pagination\AbstractPaginator ): ?>
    <div class="d-flex justify-content-center mt-3">
        <?php echo e($rows->links()); ?>

    </div>
<?php endif; ?>


<?php /**PATH D:\Workstation\Taswk\sorriso-backend\resources\views/admin/clients/table.blade.php ENDPATH**/ ?>