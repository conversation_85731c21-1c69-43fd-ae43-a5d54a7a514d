<?php

namespace App\Jobs;

use App\Models\Order;
use App\Models\Admin;
use App\Models\Service;
use App\Jobs\AdminNotify;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Carbon\Carbon;

class CheckServiceTimeouts implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Get orders with services that have exceeded their expected completion time
        $timeoutOrders = Order::where('payment_status', Order::PAY_STATUS_PAID)
            ->whereIn('current_status', ['confirmed', 'processing'])
            ->whereHas('orderItems', function($query) {
                $query->where('item_type', 'App\Models\Service');
            })
            ->whereDoesntHave('orderNotifications', function($query) {
                $query->where('type', 'service_timeout')
                    ->where('created_at', '>=', Carbon::now()->subDay());
            })
            ->with(['user', 'provider', 'orderItems'])
            ->get();

        $admins = Admin::where('is_active', true)->get();

        foreach ($timeoutOrders as $order) {
            $hasTimedOutService = false;
            $timedOutServices = [];

            foreach ($order->orderItems as $item) {
                if ($item->item_type === 'App\Models\Service') {
                    $service = Service::find($item->item_id);
                    
                    if ($service && $service->duration) {
                        // Calculate expected completion time
                        $expectedCompletionTime = $order->scheduled_at 
                            ? Carbon::parse($order->scheduled_at)->addMinutes($service->duration)
                            : $order->created_at->addMinutes($service->duration);

                        // Check if service has timed out
                        if (Carbon::now()->gt($expectedCompletionTime)) {
                            $hasTimedOutService = true;
                            $timedOutServices[] = $service->name;
                        }
                    }
                }
            }

            if ($hasTimedOutService) {
                $serviceNames = implode(', ', $timedOutServices);
                
                // Create notification data
                $notificationData = [
                    'title' => 'انتهاء وقت الخدمة',
                    'body' => "الطلب رقم {$order->order_number} للعميل {$order->user->name} تجاوز الوقت المخصص للخدمات: {$serviceNames}. مزود الخدمة: {$order->provider->commercial_name}",
                    'type' => 'service_timeout',
                    'order_id' => $order->id,
                    'user_id' => $order->user_id,
                    'provider_id' => $order->provider_id,
                    'services' => $timedOutServices,
                ];

                // Send notification to admins
                dispatch(new AdminNotify($admins, $notificationData));

                // Mark order as notified to avoid duplicate notifications
                $order->orderNotifications()->create([
                    'type' => 'service_timeout',
                    'data' => $notificationData,
                    'sent_at' => Carbon::now(),
                ]);
            }
        }
    }
}
