<?php

namespace App\Console\Commands;

use App\Models\Order;
use Illuminate\Console\Command;
use Carbon\Carbon;

class CleanupExpiredPendingOrders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'orders:cleanup-expired-pending';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cancel expired pending payment orders and restore product quantities';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $expiredMinutes = config('app.pending_order_expiry_minutes', 30); // Default 30 minutes
        $expiredTime = Carbon::now()->subMinutes($expiredMinutes);

        // Get expired orders that are soft deleted (locked for online payment)
        $expiredOrders = Order::onlyTrashed()
            ->where('payment_status', Order::PAY_STATUS_PENDING)
            ->where('current_status', 'pending_payment')
            ->where('created_at', '<=', $expiredTime)
            ->get();

        $cancelledCount = 0;

        foreach ($expiredOrders as $order) {
            try {
                // Cancel the order and restore quantities
                $order->update([
                    'current_status' => 'cancelled',
                    'payment_status' => Order::PAY_STATUS_CANCELLED,
                    'cancelled_at' => Carbon::now(),
                    'cancellation_reason' => 'Payment timeout - automatically cancelled'
                ]);

                // Restore product quantities
                foreach ($order->items as $item) {
                    if ($item->item_type === 'App\Models\Product') {
                        $product = $item->item;
                        if ($product) {
                            $product->increment('quantity', $item->quantity);
                        }
                    }
                }

                $cancelledCount++;
                $this->info("Cancelled expired order: {$order->order_number}");

            } catch (\Exception $e) {
                $this->error("Failed to cancel order {$order->order_number}: {$e->getMessage()}");
            }
        }

        $this->info("Cleanup completed. Cancelled {$cancelledCount} expired orders.");
        
        return 0;
    }
}
