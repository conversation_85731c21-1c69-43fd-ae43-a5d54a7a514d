<?php

namespace App\Console\Commands;

use App\Jobs\CheckPaymentDelays;
use App\Jobs\CheckServiceTimeouts;
use Illuminate\Console\Command;

class CheckOrderNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'orders:check-notifications';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check for payment delays and service timeouts and send notifications to admins';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking for payment delays...');
        dispatch(new CheckPaymentDelays());
        
        $this->info('Checking for service timeouts...');
        dispatch(new CheckServiceTimeouts());
        
        $this->info('Order notification checks completed.');
        
        return 0;
    }
}
