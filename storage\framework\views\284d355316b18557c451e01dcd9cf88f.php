<div class="position-relative">
    
    
    
    
    <table class="table " id="tab">
        <thead>
            <tr>
                <th>#</th>
                <th><?php echo e(__('admin.name')); ?></th>
                <th><?php echo e(__('admin.control')); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr class="delete_role">
                    <td><?php echo e($loop->iteration); ?></td>
                    <td><?php echo e($role->name); ?></td>
                    <td class="product-action">
                        <span class="d-none d-md-inline">
                            <a href="<?php echo e(route('admin.roles.edit', ['id' => $role->id])); ?>" class="btn btn-primary btn-sm p-1" title="<?php echo e(__('admin.edit')); ?>"><i class="feather icon-edit"></i></a>
                        </span>
                        <?php if(auth()->guard('admin')->user()->role->id != $role->id): ?>
                            <span class="d-none d-md-inline">
                                <button class="delete-row btn btn-danger btn-sm p-1" data-url="<?php echo e(url('admin/roles/' . $role->id)); ?>" title="<?php echo e(__('admin.delete')); ?>"><i class="feather icon-trash"></i></button>
                            </span>
                        <?php endif; ?>

                        <span class="actions-dropdown d-md-none">
                            <div class="dropdown">
                                <button class="btn btn-sm btn-secondary dropdown-toggle" type="button" id="actions-menu-<?php echo e($role->id); ?>" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <?php echo e(__('admin.actions')); ?>

                                </button>
                                <div class="dropdown-menu" aria-labelledby="actions-menu-<?php echo e($role->id); ?>">
                                    <a class="dropdown-item" href="<?php echo e(route('admin.roles.edit', ['id' => $role->id])); ?>"><?php echo e(__('admin.edit')); ?></a>
                                    <?php if(auth()->guard('admin')->user()->role->id != $role->id): ?>
                                        <button class="dropdown-item delete-row" data-url="<?php echo e(url('admin/roles/' . $role->id)); ?>"><?php echo e(__('admin.delete')); ?></button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </span>
                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>
    
    
    <?php if($roles->count() == 0): ?>
        <div class="d-flex flex-column w-100 align-center mt-4">
            <img src="<?php echo e(asset('admin/app-assets/images/pages/404.png')); ?>" alt="">
            <span class="mt-2" style="font-family: cairo"><?php echo e(__('admin.there_are_no_matches_matching')); ?></span>
        </div>
    <?php endif; ?>
    

</div>

<?php if($roles->count() > 0 && $roles instanceof \Illuminate\Pagination\AbstractPaginator ): ?>
    <div class="d-flex justify-content-center mt-3">
        <?php echo e($roles->links()); ?>

    </div>
<?php endif; ?>
<?php /**PATH D:\Workstation\Taswk\sorriso-backend\resources\views/admin/roles/table.blade.php ENDPATH**/ ?>